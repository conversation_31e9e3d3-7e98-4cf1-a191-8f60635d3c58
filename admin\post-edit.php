<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header('Location: ../login.php');
    exit();
}

// Include database connection
include_once __DIR__ . '/../db_conn.php';

// Include data handlers
include_once __DIR__ . '/data/post.php';
include_once __DIR__ . '/data/category.php';

// Check if ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: post.php?error=' . urlencode('ID postingan tidak valid'));
    exit();
}

$id = intval($_GET['id']);
$errors = [];

// Get post data
try {
    $post = function_exists('getPostById') ? getPostById($conn, $id) : null;
    if (!$post) {
        // Fallback to direct query - prioritas tabel 'post' dulu
        $stmt = $conn->prepare("SELECT * FROM post WHERE id = ?");
        $stmt->execute([$id]);
        $post = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$post) {
            // Try with 'posts' table name sebagai fallback
            $stmt = $conn->prepare("SELECT * FROM posts WHERE id = ?");
            $stmt->execute([$id]);
            $post = $stmt->fetch(PDO::FETCH_ASSOC);
        }
    }

    if (!$post) {
        header('Location: post.php?error=' . urlencode('Postingan tidak ditemukan'));
        exit();
    }
} catch (Exception $e) {
    header('Location: post.php?error=' . urlencode('Error: ' . $e->getMessage()));
    exit();
}

// Get categories
try {
    $categories = function_exists('getAllCategories') ? getAllCategories($conn) : [];
} catch (Exception $e) {
    $categories = [];
    $errors[] = 'Error mengambil data kategori: ' . $e->getMessage();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = isset($_POST['title']) ? trim($_POST['title']) : '';
    $content = isset($_POST['content']) ? trim($_POST['content']) : '';
    $category_id = isset($_POST['category_id']) ? intval($_POST['category_id']) : 0;
    $cover_url = $post['cover_url'];
    // Upload cover baru jika ada
    $uploadDir = realpath(__DIR__ . '/../upload/blog');
    if ($uploadDir === false) {
        $baseUpload = __DIR__ . '/../upload/blog';
        if (!is_dir($baseUpload)) {
            mkdir($baseUpload, 0777, true);
        }
        $uploadDir = realpath($baseUpload);
    }
    if (isset($_FILES['cover']) && $_FILES['cover']['error'] === UPLOAD_ERR_OK) {
        $ext = pathinfo($_FILES['cover']['name'], PATHINFO_EXTENSION);
        $filename = 'post_' . time() . '.' . $ext;
        $target = $uploadDir . DIRECTORY_SEPARATOR . $filename;
        if (move_uploaded_file($_FILES['cover']['tmp_name'], $target)) {
            $cover_url = $filename;
        }
    }
    // Update ke database
    $stmt = $conn->prepare("UPDATE post SET post_title=?, post_text=?, category_id=?, cover_url=? WHERE id=?");
    $stmt->execute([$title, $content, $category_id, $cover_url, $id]);
    header('Location: post.php');
    exit();
}

// Include header setelah semua redirect logic
include __DIR__ . '/inc/header.php';
?>
<div class="dashboard-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="welcome-header">
            <i class="fa fa-edit mr-3"></i>Edit Postingan
        </h1>
        <a href="post.php" class="btn btn-secondary">
            <i class="fa fa-arrow-left mr-2"></i>Kembali
        </a>
    </div>

    <?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <i class="fa fa-exclamation-circle mr-2"></i>
        <strong>Terjadi kesalahan:</strong>
        <ul class="mb-0 mt-2">
            <?php foreach ($errors as $error): ?>
            <li><?= htmlspecialchars($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php endif; ?>

    <div class="card summary-card">
        <div class="card-header card-header-brand-warning">
            <i class="fa fa-edit mr-2"></i>Form Edit Postingan
        </div>
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="title"><i class="fa fa-heading mr-1"></i>Judul Postingan *</label>
                    <input type="text" class="form-control" id="title" name="title"
                           value="<?= htmlspecialchars($post['post_title']) ?>"
                           placeholder="Masukkan judul postingan" required>
                </div>

                <div class="form-group">
                    <label for="content"><i class="fa fa-file-text mr-1"></i>Konten Postingan *</label>
                    <textarea class="form-control" id="content" name="content" rows="8"
                              placeholder="Tulis konten postingan di sini..." required><?= htmlspecialchars($post['post_text']) ?></textarea>
                </div>

                <div class="form-group">
                    <label for="category_id"><i class="fa fa-folder mr-1"></i>Kategori *</label>
                    <select class="form-control" id="category_id" name="category_id" required>
                        <option value="">Pilih Kategori</option>
                        <?php foreach ($categories as $cat): ?>
                            <option value="<?= intval($cat['id']) ?>"
                                    <?= $cat['id'] == $post['category_id'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($cat['category']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="form-group">
                    <label for="cover"><i class="fa fa-image mr-1"></i>Cover Image</label>
                    <?php if (!empty($post['cover_url'])): ?>
                    <div class="mb-3">
                        <img src="../upload/blog/<?= htmlspecialchars($post['cover_url']) ?>"
                             alt="Current cover" class="img-thumbnail" style="max-width: 200px;">
                        <small class="form-text text-muted">Cover saat ini</small>
                    </div>
                    <?php endif; ?>
                    <input type="file" class="form-control-file" id="cover" name="cover" accept="image/*">
                    <small class="form-text text-muted">Upload gambar baru untuk mengganti cover (opsional)</small>
                </div>

                <hr>

                <div class="form-group mb-0">
                    <button type="submit" class="btn btn-brand-warning">
                        <i class="fa fa-save mr-2"></i>Update Postingan
                    </button>
                    <a href="post.php" class="btn btn-secondary ml-2">
                        <i class="fa fa-times mr-2"></i>Batal
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="../js/jquery-1.12.4.min.js"></script>
<script src="../js/bootstrap.min.js"></script>

</body>
</html>
