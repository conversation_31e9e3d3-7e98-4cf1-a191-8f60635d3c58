<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit();
}

// Include database connection
include_once __DIR__ . '/../../db_conn.php';

header('Content-Type: application/json');

try {
    $stats = [
        'pengurus_count' => 0,
        'users_count' => 0,
        'posts_count' => 0,
        'categories_count' => 0,
        'comments_count' => 0,
        'total_count' => 0
    ];
    
    // Count pengurus
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM pengurus");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats['pengurus_count'] = intval($result['count'] ?? 0);
    } catch (Exception $e) {
        $stats['pengurus_count'] = 0;
    }
    
    // Count users
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM users");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats['users_count'] = intval($result['count'] ?? 0);
    } catch (Exception $e) {
        $stats['users_count'] = 0;
    }
    
    // Count posts - prioritas tabel 'post' terlebih dahulu
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM post");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats['posts_count'] = intval($result['count'] ?? 0);
    } catch (Exception $e) {
        try {
            $stmt = $conn->query("SELECT COUNT(*) as count FROM posts");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $stats['posts_count'] = intval($result['count'] ?? 0);
        } catch (Exception $e2) {
            $stats['posts_count'] = 0;
        }
    }
    
    // Count categories - prioritas tabel 'category' terlebih dahulu
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM category");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats['categories_count'] = intval($result['count'] ?? 0);
    } catch (Exception $e) {
        try {
            $stmt = $conn->query("SELECT COUNT(*) as count FROM categories");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $stats['categories_count'] = intval($result['count'] ?? 0);
        } catch (Exception $e2) {
            $stats['categories_count'] = 0;
        }
    }
    
    // Count comments
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM comments");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats['comments_count'] = intval($result['count'] ?? 0);
    } catch (Exception $e) {
        $stats['comments_count'] = 0;
    }
    
    // Calculate total
    $stats['total_count'] = $stats['pengurus_count'] + $stats['users_count'] + $stats['posts_count'] + $stats['categories_count'] + $stats['comments_count'];
    
    // Add timestamp
    $stats['timestamp'] = date('Y-m-d H:i:s');
    $stats['success'] = true;
    
    echo json_encode($stats);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
