<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dropdown Animations Test - Keren!</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/font-awesome.min.css">
    <link rel="stylesheet" href="dropdown_animations.css">
    <style>
        body {
            padding-top: 100px;
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .demo-section {
            background: white;
            padding: 40px;
            border-radius: 20px;
            margin: 20px 0;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .animation-showcase {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
            border-left: 5px solid #007bff;
        }
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            margin: 15px 0;
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .animation-demo {
            display: inline-block;
            padding: 10px 20px;
            background: linear-gradient(135deg, #005a99, #ff9800);
            color: white;
            border-radius: 25px;
            margin: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .animation-demo:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0,90,153,0.3);
        }
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255,255,255,0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }
        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Include the enhanced navbar -->
    <?php include 'inc/NavBar.php'; ?>
    
    <div class="container">
        <div class="demo-section">
            <div class="text-center">
                <h1 class="mb-4">🎨 Dropdown Animations - Super Keren!</h1>
                <p class="lead">Dropdown navbar sekarang memiliki animasi yang sangat keren dan smooth</p>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="animation-showcase">
                    <h3 class="text-primary mb-3">🎯 Animasi Desktop</h3>
                    
                    <div class="feature-card">
                        <h5>✨ 3D Transform Animation</h5>
                        <p>Dropdown muncul dengan efek 3D rotateX dan scale yang smooth</p>
                        <code>transform: translateY(-20px) scale(0.95) rotateX(-10deg)</code>
                    </div>
                    
                    <div class="feature-card">
                        <h5>🌟 Glowing Border Effect</h5>
                        <p>Border yang berkilau dengan gradient animation</p>
                        <code>animation: borderGlow 2s ease-in-out infinite alternate</code>
                    </div>
                    
                    <div class="feature-card">
                        <h5>⚡ Staggered Item Animation</h5>
                        <p>Setiap item dropdown muncul dengan delay bertahap</p>
                        <code>animation-delay: 0.1s, 0.2s, 0.3s</code>
                    </div>
                    
                    <div class="feature-card">
                        <h5>🎪 Bounce Effect</h5>
                        <p>Efek bouncing dengan cubic-bezier timing function</p>
                        <code>cubic-bezier(0.68, -0.55, 0.265, 1.55)</code>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="animation-showcase">
                    <h3 class="text-success mb-3">📱 Animasi Mobile</h3>
                    
                    <div class="feature-card">
                        <h5>📲 Slide Down Animation</h5>
                        <p>Dropdown slide down dengan easing yang smooth</p>
                        <code>slideDown(300, 'easeInOutCubic')</code>
                    </div>
                    
                    <div class="feature-card">
                        <h5>🎨 Custom Mobile Styling</h5>
                        <p>Styling khusus untuk mobile dengan background transparan</p>
                        <code>background: rgba(255,255,255,0.1)</code>
                    </div>
                    
                    <div class="feature-card">
                        <h5>👆 Touch-Friendly</h5>
                        <p>Optimized untuk touch interaction di mobile</p>
                        <code>padding: 10px 20px; font-size: 15px</code>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3 class="text-center mb-4">🎬 Interactive Animation Demo</h3>
            <div class="text-center">
                <p class="mb-4">Klik tombol di bawah untuk melihat berbagai efek animasi:</p>
                
                <div class="animation-demo" onclick="demoSlideIn()">
                    <i class="fa fa-magic me-2"></i>Slide In Effect
                </div>
                
                <div class="animation-demo" onclick="demoBounce()">
                    <i class="fa fa-rocket me-2"></i>Bounce Effect
                </div>
                
                <div class="animation-demo" onclick="demoGlow()">
                    <i class="fa fa-star me-2"></i>Glow Effect
                </div>
                
                <div class="animation-demo" onclick="demoRipple(event)">
                    <i class="fa fa-circle-o me-2"></i>Ripple Effect
                </div>
            </div>
            
            <div id="demo-area" class="mt-4 p-4 bg-light rounded text-center">
                <p class="text-muted">Klik tombol di atas untuk melihat demo animasi</p>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="animation-showcase">
                    <h3 class="text-warning mb-3">🔧 Technical Features</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <h5>CSS Animations:</h5>
                            <ul>
                                <li>3D Transforms (rotateX, scale)</li>
                                <li>Cubic-bezier timing functions</li>
                                <li>Keyframe animations</li>
                                <li>Backdrop-filter blur effect</li>
                                <li>Gradient border animations</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5>JavaScript Enhancements:</h5>
                            <ul>
                                <li>Staggered animation delays</li>
                                <li>Hover timeout management</li>
                                <li>Ripple effect on click</li>
                                <li>Responsive behavior switching</li>
                                <li>Smooth mobile slide animations</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <div class="alert alert-success">
                <h4 class="alert-heading">🎉 Dropdown Animations Completed!</h4>
                <p>Dropdown navbar sekarang memiliki animasi yang sangat keren:</p>
                <hr>
                <div class="row">
                    <div class="col-md-6">
                        <h6>Desktop Features:</h6>
                        <ul class="mb-0">
                            <li>3D transform entrance</li>
                            <li>Glowing border effect</li>
                            <li>Staggered item animations</li>
                            <li>Smooth hover delays</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Mobile Features:</h6>
                        <ul class="mb-0">
                            <li>Smooth slide animations</li>
                            <li>Touch-optimized styling</li>
                            <li>Custom mobile behavior</li>
                            <li>Responsive breakpoints</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/vendor/jquery-1.12.4.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    
    <script>
        // Demo animation functions
        function demoSlideIn() {
            $('#demo-area').html('<div class="alert alert-info" style="animation: dropdownSlideIn 0.5s ease;"><i class="fa fa-magic me-2"></i>Slide In Animation Demo!</div>');
        }
        
        function demoBounce() {
            $('#demo-area').html('<div class="alert alert-success" style="animation: bounce 1s ease;"><i class="fa fa-rocket me-2"></i>Bounce Animation Demo!</div>');
        }
        
        function demoGlow() {
            $('#demo-area').html('<div class="alert alert-warning" style="animation: borderGlow 2s ease infinite alternate; border: 3px solid #ff9800;"><i class="fa fa-star me-2"></i>Glow Animation Demo!</div>');
        }
        
        function demoRipple(e) {
            var $target = $(e.target).closest('.animation-demo');
            var $ripple = $('<span class="ripple"></span>');
            
            $target.append($ripple);
            
            var btnOffset = $target.offset();
            var xPos = e.pageX - btnOffset.left;
            var yPos = e.pageY - btnOffset.top;
            
            $ripple.css({
                top: yPos,
                left: xPos,
                width: '20px',
                height: '20px'
            });
            
            $('#demo-area').html('<div class="alert alert-primary"><i class="fa fa-circle-o me-2"></i>Ripple Effect Demo! (Check the button)</div>');
            
            setTimeout(function() {
                $ripple.remove();
            }, 600);
        }
        
        // Add bounce keyframe
        $('<style>').prop('type', 'text/css').html(`
            @keyframes bounce {
                0%, 20%, 53%, 80%, 100% {
                    transform: translate3d(0,0,0);
                }
                40%, 43% {
                    transform: translate3d(0,-30px,0);
                }
                70% {
                    transform: translate3d(0,-15px,0);
                }
                90% {
                    transform: translate3d(0,-4px,0);
                }
            }
        `).appendTo('head');
        
        $(document).ready(function() {
            // Show current screen size
            function updateScreenInfo() {
                var width = $(window).width();
                var type = width >= 992 ? 'Desktop' : width >= 768 ? 'Tablet' : 'Mobile';
                $('#screen-info').text(type + ' (' + width + 'px)');
            }
            
            $('body').prepend('<div id="screen-info" style="position: fixed; top: 10px; right: 10px; background: rgba(0,0,0,0.8); color: white; padding: 5px 10px; border-radius: 5px; font-size: 12px; z-index: 9999;"></div>');
            updateScreenInfo();
            $(window).resize(updateScreenInfo);
        });
    </script>
</body>
</html>
