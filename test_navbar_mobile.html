<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navbar Mobile Test - UKM Panahan Gendewa Geni</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/font-awesome.min.css">
    <style>
        body {
            padding-top: 80px;
            font-family: 'Roboto', sans-serif;
        }
        .test-section {
            padding: 40px 0;
            background: #f8f9fa;
            margin: 20px 0;
            border-radius: 10px;
        }
        .feature-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .feature-item {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-item:last-child {
            border-bottom: none;
        }
        .test-instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <!-- Include the enhanced navbar -->
    <?php include 'inc/NavBar.php'; ?>
    
    <div class="container">
        <div class="test-section">
            <div class="text-center">
                <h1 class="mb-4">🔧 Navbar Mobile Responsiveness Test</h1>
                <p class="lead">Test halaman untuk memverifikasi perbaikan navbar mobile</p>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6">
                <div class="feature-list">
                    <h3 class="text-primary mb-3">✅ Fitur yang Diperbaiki</h3>
                    <div class="feature-item">
                        <strong>🍔 Hamburger Menu:</strong> Tombol menu yang lebih responsif dengan animasi smooth
                    </div>
                    <div class="feature-item">
                        <strong>📱 Mobile Layout:</strong> Menu mobile dengan background gelap dan padding yang nyaman
                    </div>
                    <div class="feature-item">
                        <strong>🎨 Visual Improvements:</strong> Icons pada menu mobile, hover effects, dan transisi smooth
                    </div>
                    <div class="feature-item">
                        <strong>👤 User Profile:</strong> Avatar dan nama user yang lebih rapi di mobile
                    </div>
                    <div class="feature-item">
                        <strong>🔽 Dropdown Menu:</strong> Dropdown yang bekerja dengan baik di mobile dan desktop
                    </div>
                    <div class="feature-item">
                        <strong>🎯 Active States:</strong> Highlighting halaman aktif dan hover effects
                    </div>
                    <div class="feature-item">
                        <strong>📏 Responsive Breakpoints:</strong> Optimasi untuk tablet (768px) dan mobile (576px)
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="feature-list">
                    <h3 class="text-success mb-3">🚀 Peningkatan Performa</h3>
                    <div class="feature-item">
                        <strong>⚡ Smooth Animations:</strong> Transisi halus untuk semua interaksi
                    </div>
                    <div class="feature-item">
                        <strong>🎪 Auto-close Menu:</strong> Menu mobile otomatis tertutup saat link diklik
                    </div>
                    <div class="feature-item">
                        <strong>🌊 Scroll Effects:</strong> Navbar background berubah saat scroll
                    </div>
                    <div class="feature-item">
                        <strong>🎨 Brand Colors:</strong> Konsisten dengan warna brand (#005a99, #ff9800)
                    </div>
                    <div class="feature-item">
                        <strong>📐 Better Spacing:</strong> Padding dan margin yang optimal untuk touch devices
                    </div>
                    <div class="feature-item">
                        <strong>🔧 Cross-browser:</strong> Kompatibel dengan semua browser modern
                    </div>
                </div>
            </div>
        </div>

        <div class="test-instructions">
            <h3 class="text-warning mb-3">📋 Cara Testing</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>Desktop Testing:</h5>
                    <ul>
                        <li>Hover pada menu items untuk melihat efek</li>
                        <li>Klik dropdown "Tentang Kami" untuk melihat animasi</li>
                        <li>Scroll halaman untuk melihat efek navbar</li>
                        <li>Resize browser untuk melihat responsive behavior</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>Mobile Testing:</h5>
                    <ul>
                        <li>Buka di mobile device atau gunakan DevTools</li>
                        <li>Klik hamburger menu (☰) untuk membuka menu</li>
                        <li>Test semua menu items dan dropdown</li>
                        <li>Verifikasi menu tertutup otomatis setelah klik</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="feature-list">
                    <h3 class="text-info mb-3">📱 Responsive Breakpoints</h3>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Screen Size</th>
                                    <th>Behavior</th>
                                    <th>Features</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><strong>Desktop (≥992px)</strong></td>
                                    <td>Horizontal menu</td>
                                    <td>Hover effects, dropdown animations, full logo</td>
                                </tr>
                                <tr>
                                    <td><strong>Tablet (768px-991px)</strong></td>
                                    <td>Hamburger menu</td>
                                    <td>Collapsible menu, medium logo, touch-friendly</td>
                                </tr>
                                <tr>
                                    <td><strong>Mobile (≤767px)</strong></td>
                                    <td>Full mobile menu</td>
                                    <td>Icons, compact layout, optimized spacing</td>
                                </tr>
                                <tr>
                                    <td><strong>Small Mobile (≤576px)</strong></td>
                                    <td>Ultra-compact</td>
                                    <td>Smaller logo, reduced padding, optimized text</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="text-center">
                <h3 class="text-success">✅ Navbar Mobile Sudah Diperbaiki!</h3>
                <p class="mb-4">Navbar sekarang fully responsive dan mobile-friendly dengan semua fitur yang diminta.</p>
                <div class="row">
                    <div class="col-md-4">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <i class="fa fa-mobile fa-3x text-primary mb-3"></i>
                                <h5>Mobile Optimized</h5>
                                <p>Perfect untuk semua ukuran layar mobile</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <i class="fa fa-tablet fa-3x text-success mb-3"></i>
                                <h5>Tablet Ready</h5>
                                <p>Responsive design untuk tablet</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <i class="fa fa-desktop fa-3x text-warning mb-3"></i>
                                <h5>Desktop Enhanced</h5>
                                <p>Improved desktop experience</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/vendor/jquery-1.12.4.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    
    <script>
        // Additional test functionality
        $(document).ready(function() {
            // Show current screen size
            function showScreenSize() {
                var width = $(window).width();
                var size = '';
                if (width >= 992) size = 'Desktop';
                else if (width >= 768) size = 'Tablet';
                else if (width >= 576) size = 'Mobile';
                else size = 'Small Mobile';
                
                $('#screen-size').text(size + ' (' + width + 'px)');
            }
            
            // Add screen size indicator
            $('body').prepend('<div id="screen-indicator" style="position: fixed; top: 10px; right: 10px; background: rgba(0,0,0,0.8); color: white; padding: 5px 10px; border-radius: 5px; font-size: 12px; z-index: 9999;"><span id="screen-size"></span></div>');
            
            showScreenSize();
            $(window).resize(showScreenSize);
            
            // Test menu functionality
            $('.navbar-nav .nav-link').on('click', function() {
                console.log('Menu clicked:', $(this).text().trim());
            });
        });
    </script>
</body>
</html>
