<?php 
session_start();
$logged = false;
if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
	 $logged = true;
	 $user_id = $_SESSION['user_id'];
    }
  $notFound = 0;
 ?>
<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>
		<?php 
		if(isset($_GET['search'])){ 
			  echo "search '".htmlspecialchars($_GET['search'])."'"; 
		}else{
			echo "Blog Page";
		} ?>
	</title>
	<link rel="stylesheet" href="css/bootstrap.min.css">
	<link rel="stylesheet" href="css/font-awesome.min.css">
	<link rel="stylesheet" href="css/style.css">
	<link rel="stylesheet" href="css/responsive.css">
	<!-- Custom CSS for category/berita page -->
	<style>
	body {
	    background: #fff !important;
	    min-height: 100vh;
	    font-family: 'Poppins', Arial, sans-serif;
	}
	.category-aside {
	    border-radius: 1rem;
	    overflow: hidden;
	    background: #fff;
	    box-shadow: 0 4px 24px 0 rgba(0,90,153,0.08);
	}
	.category-aside .list-group-item {
	    border: none;
	    background: transparent;
	    transition: background 0.2s, color 0.2s;
	    font-weight: 600;
	    color: #005a99;
	    font-size: 1.05rem;
	}
	.category-aside .list-group-item.active,
	.category-aside .list-group-item:active {
	    background: linear-gradient(90deg, #005a99 0%, #ff8800 100%);
	    color: #fff;
	    border: none;
	    box-shadow: 0 2px 8px #005a9922;
	}
	.category-aside .list-group-item:not(.active):hover {
	    background: #f0f4fa;
	    color: #ff8800;
	}
	.main-blog-card {
	    border-radius: 1.2rem;
	    overflow: hidden;
	    box-shadow: 0 4px 24px 0 rgba(0,90,153,0.10);
	    transition: transform 0.22s, box-shadow 0.22s;
	    background: #fff;
	    border: none;
	    display: flex;
	    flex-direction: column;
	    height: 100%;
	}
	.main-blog-card:hover {
	    transform: translateY(-6px);
	    box-shadow: 0 12px 36px 0 rgba(0,90,153,0.18);
	}
	.category-badge {
	    background: linear-gradient(90deg, #005a99 0%, #ff8800 100%);
	    color: white;
	    padding: 6px 12px;
	    border-radius: 20px;
	    font-size: 0.8rem;
	    font-weight: 600;
	    box-shadow: 0 2px 8px rgba(0,90,153,0.3);
	}
	.page-header {
	    background: linear-gradient(135deg, #005a99 0%, #ff8800 100%);
	    color: white;
	    padding: 60px 0;
	    margin-bottom: 40px;
	}
	.page-header h1 {
	    font-size: 2.5rem;
	    font-weight: 700;
	    margin-bottom: 16px;
	}
	.page-header p {
	    font-size: 1.1rem;
	    opacity: 0.9;
	}
	.main-blog-card .card-title {
	    font-size: 1.13rem;
	    font-weight: 700;
	    color: #005a99;
	    margin-bottom: 8px;
	    letter-spacing: 0.5px;
	}
	.main-blog-card .card-text {
	    color: #444;
	    font-size: 1.01rem;
	    margin-bottom: 0;
	}
	.main-blog-card .btn-primary {
	    background: linear-gradient(90deg, #005a99 60%, #ff8800 100%);
	    border: none;
	    border-radius: 2rem;
	    font-weight: 600;
	    transition: background 0.2s;
	    letter-spacing: 0.5px;
	}
	.main-blog-card .btn-primary:hover {
	    background: linear-gradient(90deg, #ff8800 0%, #005a99 100%);
	}
	.main-blog-card .card-footer {
	    background: #f8fafc;
	    border-top: none;
	    font-size: 0.97rem;
	    color: #005a99;
	}
	.category-badge, .badge-category {
	    display: inline-block;
	    background: linear-gradient(90deg, #005a99 0%, #ff8800 100%);
	    color: #fff;
	    border-radius: 1rem;
	    padding: 0.32em 1em;
	    font-size: 0.93em;
	    font-weight: 600;
	    margin-bottom: 0.7em;
	    box-shadow: 0 2px 8px #005a9922;
	    letter-spacing: 0.5px;
	}
	.react-btns .fa {
	    cursor: pointer;
	    margin-right: 0.5em;
	    transition: color 0.2s;
	    font-size: 1.1em;
	}
	.react-btns .fa.liked {
	    color: #e74c3c;
	}
	.react-btns .fa:hover {
	    color: #ff8800;
	}
	/* Enhanced responsive design */
	@media (max-width: 991px) {
	    .category-aside {
	        margin-bottom: 2rem;
	    }
	    .pt-100 { padding-top: 80px !important; }
	    .pb-60 { padding-bottom: 40px !important; }
	    h1 { font-size: 2rem !important; }
	    .lead { font-size: 1.1rem !important; }
	}

	@media (max-width: 767px) {
	    .navbar-brand img { max-width: 90px; }

	    /* Header responsive */
	    h1 { font-size: 1.8rem !important; }
	    .lead { font-size: 1rem !important; }
	    .pt-100 { padding-top: 60px !important; }
	    .pb-60 { padding-bottom: 30px !important; }

	    /* Cards responsive */
	    .main-blog-card .card-title {
	        font-size: 1rem;
	        height: auto;
	        -webkit-line-clamp: 2;
        line-clamp: 2;
	    }
	    .main-blog-card .card-text {
	        font-size: 0.95rem;
	    }
	    .category-badge {
	        font-size: 0.8em;
	        padding: 0.25em 0.8em;
	    }

	    /* Latest news slider */
	    #latestNewsSlider .carousel-inner {
	        height: 200px !important;
	    }
	    #latestNewsSlider img {
	        height: 200px !important;
	    }
	}

	@media (max-width: 575px) {
	    .navbar-brand img { max-width: 70px; }
	    .footer, footer { font-size: 0.92rem; padding: 12px 2px !important; }

	    /* Header mobile */
	    h1 { font-size: 1.6rem !important; }
	    .lead { font-size: 0.95rem !important; }
	    .pt-100 { padding-top: 50px !important; }
	    .pb-60 { padding-bottom: 25px !important; }

	    /* Cards mobile */
	    .main-blog-card {
	        margin-bottom: 1.5rem;
	    }
	    .main-blog-card .card-title {
	        font-size: 0.95rem;
	        height: auto;
	    }
	    .main-blog-card .card-text {
	        font-size: 0.9rem;
	        line-height: 1.4;
	    }
	    .main-blog-card img {
	        height: 160px !important;
	    }
	    .category-badge {
	        font-size: 0.75em;
	        padding: 0.2em 0.6em;
	    }

	    /* Sidebar mobile */
	    .category-aside .list-group-item {
	        font-size: 0.95rem;
	        padding: 0.75rem 1rem;
	    }

	    /* Latest news mobile */
	    #latestNewsSlider .carousel-inner {
	        height: 150px !important;
	    }
	    #latestNewsSlider img {
	        height: 150px !important;
	    }

	    /* Statistics mobile */
	    .col-md-3.col-6 {
	        margin-bottom: 1rem;
	    }
	}
	</style>
</head>
<body>
	<?php 
      include 'inc/navbar.php';
      include_once("admin/data/post.php");
      include_once("admin/data/comment.php");
      include_once("admin/data/category.php");
      include_once("admin/data/like.php");
      include_once("db_conn.php");

      // Enhanced data retrieval with better error handling
      try {
          // Get categories first
          $categories = function_exists('getAllCategories') ? getAllCategories($conn) : [];

          // Get posts based on filters
          if(isset($_GET['category_id'])){
               $cat_id = intval($_GET['category_id']);
               $posts = function_exists('getAllPosts') ? array_filter(getAllPosts($conn), function($p) use ($cat_id) {
                   return (isset($p['category_id']) && $p['category_id'] == $cat_id);
               }) : [];

               // Get category name for display
               $current_category = null;
               foreach($categories as $cat) {
                   if($cat['id'] == $cat_id) {
                       $current_category = $cat;
                       break;
                   }
               }
          } else if(isset($_GET['search'])){
               $key = $_GET['search'];
               $posts = function_exists('searchPosts') ? searchPosts($conn, $key) : [];
               if ($posts == 0 || $posts === false) {
                     $notFound = 1;
                     $posts = [];
               }
          } else {
             $posts = function_exists('getAllPosts') ? getAllPosts($conn) : [];
          }

          // Sort posts by created_at DESC
          if(is_array($posts) && count($posts) > 0) {
              usort($posts, function($a, $b) {
                  $dateA = isset($a['created_at']) ? strtotime($a['created_at']) : 0;
                  $dateB = isset($b['created_at']) ? strtotime($b['created_at']) : 0;
                  return $dateB - $dateA;
              });
          }

          // Get statistics
          $stats = [
              'total_posts' => count($posts),
              'total_categories' => count($categories)
          ];

      } catch (Exception $e) {
          $posts = [];
          $categories = [];
          $stats = ['total_posts' => 0, 'total_categories' => 0];
          error_log("Error in category.php: " . $e->getMessage());
      }
	 ?>
       <div class="preloader">
        <div class="loader rubix-cube">
            <div class="layer layer-1"></div>
            <div class="layer layer-2"></div>
            <div class="layer layer-3 color-1"></div>
            <div class="layer layer-4"></div>
            <div class="layer layer-5"></div>
            <div class="layer layer-6"></div>
            <div class="layer layer-7"></div>
            <div class="layer layer-8"></div>
        </div>
    </div>
    <!-- Page Header -->
    <section class="pt-100 pb-60" style="background: linear-gradient(rgba(0,90,153,0.9), rgba(255,152,0,0.9)), url('upload/services-img.jpg'); background-size: cover; background-position: center;">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center">
                    <h1 class="text-white mb-3" style="font-size: 2.5rem; font-weight: 700; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                        <i class="fa fa-newspaper mr-3"></i>
                        <?php if(isset($_GET['category_id']) && isset($current_category)): ?>
                            Kategori: <?= htmlspecialchars($current_category['category']) ?>
                        <?php elseif(isset($_GET['search'])): ?>
                            Hasil Pencarian: "<?= htmlspecialchars($_GET['search']) ?>"
                        <?php else: ?>
                            Berita & Artikel
                        <?php endif; ?>
                    </h1>
                    <p class="text-white lead mb-4" style="text-shadow: 1px 1px 2px rgba(0,0,0,0.5);">
                        <?php if(isset($_GET['category_id']) && isset($current_category)): ?>
                            Menampilkan <?= $stats['total_posts'] ?> artikel dalam kategori ini
                        <?php elseif(isset($_GET['search'])): ?>
                            Ditemukan <?= $stats['total_posts'] ?> artikel yang sesuai
                        <?php else: ?>
                            Kumpulan berita dan artikel terbaru UKM Panahan Gendewa Geni
                        <?php endif; ?>
                    </p>
                    <div class="row justify-content-center">
                        <div class="col-md-3 col-6 mb-3">
                            <div class="text-center">
                                <h3 class="text-white font-weight-bold mb-1"><?= $stats['total_posts'] ?></h3>
                                <p class="text-white-50 mb-0">Total Artikel</p>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mb-3">
                            <div class="text-center">
                                <h3 class="text-white font-weight-bold mb-1"><?= $stats['total_categories'] ?></h3>
                                <p class="text-white-50 mb-0">Kategori</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="container mt-5">
    <section class="row">
        <aside class="col-lg-3 col-md-4 mb-4 order-1 order-md-1">
            <div class="list-group category-aside shadow-sm mb-4">
                <a href="#" class="list-group-item list-group-item-action active text-center" aria-current="true">
                    <i class="fa fa-list"></i> Kategori
                </a>
                <a href="category.php" class="list-group-item list-group-item-action<?= !isset($_GET['category_id']) ? ' active' : '' ?>">
                     Semua Kategori
                </a>
                <?php foreach (
                    $categories as $category ) { ?>
                <a href="category.php?category_id=<?= isset($category['id']) ? intval($category['id']) : 0 ?>" 
                   class="list-group-item list-group-item-action<?= (isset($_GET['category_id']) && intval($_GET['category_id']) == intval($category['id'])) ? ' active' : '' ?>">
                    <?= isset($category['category']) ? htmlspecialchars($category['category']) : '-' ?>
                </a>
                <?php } ?>
            </div>
            <!-- Berita terbaru autoslider, hanya gambar, dipindah ke bawah kategori -->
            <div class="card border-0 mb-4 h-100" style="width:100%;height:340px;overflow:hidden;background:transparent !important;box-shadow:none !important;border:none !important;">
                <div class="card-header bg-gradient text-white py-2 px-3 d-flex align-items-center" style="font-size:1.08rem;border-top-left-radius:18px;border-top-right-radius:18px;background:linear-gradient(90deg,#005a99 60%,#ff8800 100%);box-shadow:0 2px 8px #005a9922;letter-spacing:0.5px;">
                    <i class="fa fa-bolt me-2"></i> <span style="font-weight:600;">Berita Terbaru</span>
                </div>
                <div id="latestNewsSlider" class="carousel slide h-100" data-bs-ride="carousel" data-bs-interval="3000">
                  <div class="carousel-inner" style="height:240px;">
                    <?php 
                    $latestPosts = function_exists('getLatestPosts') ? getLatestPosts($conn, 6) : (function_exists('getAllPosts') ? array_slice(getAllPosts($conn), 0, 6) : []);
                    $active = 'active';
                    foreach($latestPosts as $news) { ?>
                      <div class="carousel-item <?= $active ?>" style="height:300px;background:transparent !important;">
                        <a href="blog-view.php?post_id=<?= isset($news['post_id']) ? intval($news['post_id']) : (isset($news['id']) ? intval($news['id']) : 0) ?>" class="d-block w-100 h-100" style="height:300px;">
                            <img src="upload/blog/<?= isset($news['cover_url']) ? htmlspecialchars($news['cover_url']) : '' ?>" alt="" style="width:100%;height:240px;object-fit:cover;border-radius:12px;box-shadow:none !important;transition:transform 0.22s;display:block;margin:auto;background:transparent !important;" onmouseover="this.style.transform='scale(1.04)'" onmouseout="this.style.transform='scale(1)'" />
                        </a>
                      </div>
                    <?php $active = ''; } ?>
                  </div>
                
                </div>
            </div>
        </aside>
        <main class="col-lg-9 col-md-8 order-2 order-md-2 position-relative">
            <div class="row">
            <?php if (is_array($posts) && count($posts) > 0) { ?>
            <?php foreach ($posts as $post) {
                $post_id = $post['post_id'] ?? $post['id'] ?? 0;

                // Get category name from categories array
                $category_name = null;
                if(isset($post['category_id'])) {
                    foreach($categories as $cat) {
                        if($cat['id'] == $post['category_id']) {
                            $category_name = $cat['category'];
                            break;
                        }
                    }
                }

                // Format date
                $formatted_date = '';
                if(isset($post['created_at']) && $post['created_at']) {
                    $formatted_date = date('d M Y', strtotime($post['created_at']));
                }
            ?>
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 main-blog-card shadow-sm">
                        <div class="position-relative">
                            <?php if ($category_name): ?>
                            <span class="category-badge position-absolute" style="top: 15px; left: 15px; z-index: 2;">
                                <?= htmlspecialchars($category_name) ?>
                            </span>
                            <?php endif; ?>
                            <img src="upload/blog/<?= htmlspecialchars($post['cover_url'] ?? 'default.jpg') ?>"
                                 class="card-img-top"
                                 alt="<?= htmlspecialchars($post['post_title'] ?? 'Article') ?>"
                                 style="height: 200px; object-fit: cover;"
                                 onerror="this.src='upload/blog/default.jpg';">
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title mb-2" style="line-height: 1.4; height: 2.8em; overflow: hidden; display: -webkit-box; -webkit-line-clamp: 2; line-clamp: 2; -webkit-box-orient: vertical;">
                                <?= htmlspecialchars($post['post_title'] ?? 'Untitled') ?>
                            </h5>
                            <?php
                                $post_text = $post['post_text'] ?? '';
                                $excerpt = strip_tags($post_text);
                                $excerpt = substr($excerpt, 0, 120);
                            ?>
                            <p class="card-text text-muted flex-grow-1 mb-3" style="line-height: 1.5;">
                                <?= htmlspecialchars($excerpt) ?><?= strlen($post_text) > 120 ? '...' : '' ?>
                            </p>
                            <div class="mt-auto">
                                <a href="blog-view.php?post_id=<?= intval($post_id) ?>"
                                   class="btn btn-primary btn-sm w-100">
                                    <i class="fa fa-eye mr-2"></i>Baca Selengkapnya
                                </a>
                            </div>
                        </div>
                        <div class="card-footer bg-light border-0">
                            <div class="row align-items-center">
                                <div class="col-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fa fa-thumbs-up text-primary mr-2"></i>
                                        <small class="text-muted"><?= likeCountByPostID($conn, $post_id) ?> likes</small>
                                    </div>
                                </div>
                                <div class="col-6 text-right">
                                    <small class="text-muted">
                                        <i class="fa fa-calendar mr-1"></i><?= $formatted_date ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php } ?>
            <?php } else { ?>
            <div class="col-12">
                <div class="card border-0 shadow-sm text-center py-5">
                    <div class="card-body">
                        <div class="mb-4">
                            <i class="fa fa-newspaper" style="font-size: 4rem; color: #ddd;"></i>
                        </div>
                        <?php if($notFound): ?>
                            <h4 class="text-muted mb-3">Tidak Ada Hasil Pencarian</h4>
                            <p class="text-muted mb-4">
                                Tidak ditemukan artikel yang sesuai dengan kata kunci
                                <strong>"<?= htmlspecialchars($_GET['search']) ?>"</strong>
                            </p>
                            <a href="category.php" class="btn btn-primary">
                                <i class="fa fa-arrow-left mr-2"></i>Lihat Semua Artikel
                            </a>
                        <?php elseif(isset($_GET['category_id'])): ?>
                            <h4 class="text-muted mb-3">Belum Ada Artikel</h4>
                            <p class="text-muted mb-4">
                                Belum ada artikel dalam kategori ini. Silakan cek kategori lain atau kembali nanti.
                            </p>
                            <a href="category.php" class="btn btn-primary">
                                <i class="fa fa-list mr-2"></i>Lihat Semua Kategori
                            </a>
                        <?php else: ?>
                            <h4 class="text-muted mb-3">Belum Ada Artikel</h4>
                            <p class="text-muted mb-4">
                                Saat ini belum ada artikel yang dipublikasikan. Silakan kembali lagi nanti.
                            </p>
                            <a href="index.php" class="btn btn-primary">
                                <i class="fa fa-home mr-2"></i>Kembali ke Beranda
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php } ?>
            </div>
        </main>
    </section>
    </div>
  
   <script>
   	 $(document).ready(function(){
			  $(".like-btn").click(function(){
			     var post_id = $(this).attr('post-id');
			     var liked = $(this).attr('liked');

			     if (liked == 1) {
                 $(this).attr('liked', '0');
                 $(this).removeClass('liked');
			     }else {
                 $(this).attr('liked', '1');
                 $(this).addClass('liked');
			     }
			     $(this).next().load("ajax/like-unlike.php",
			     	{
			     		post_id: post_id
			     	});
			  });
		  });
   </script>
   <script src="js/jquery-1.12.4.min.js"></script>
   <script src="js/bootstrap.bundle.min.js"></script>
   <script src="js/main.js"></script>
   <script>
   // Force Bootstrap carousel to auto-slide (fix for stuck carousel)
   document.addEventListener('DOMContentLoaded', function() {
      var el = document.querySelector('#latestNewsSlider');
      if (el && typeof bootstrap !== 'undefined' && bootstrap.Carousel) {
         var carousel = bootstrap.Carousel.getOrCreateInstance(el, {interval: 3000, ride: 'carousel'});
         carousel.cycle();
      }
   });
   </script>
   <?php   include 'inc/footer.php'; ?>
</body>
</html>