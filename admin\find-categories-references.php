<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header('Location: ../login.php');
    exit();
}

$results = [];

// Function to search for 'categories' references in files
function searchInFile($filepath, $pattern) {
    if (!file_exists($filepath) || !is_readable($filepath)) {
        return [];
    }
    
    $content = file_get_contents($filepath);
    $lines = explode("\n", $content);
    $matches = [];
    
    foreach ($lines as $lineNum => $line) {
        if (preg_match($pattern, $line)) {
            $matches[] = [
                'line' => $lineNum + 1,
                'content' => trim($line)
            ];
        }
    }
    
    return $matches;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['search_files'])) {
    
    // Pattern to search for 'categories' table references
    $pattern = '/\b(FROM\s+categories|INTO\s+categories|UPDATE\s+categories|categories\s+WHERE|JOIN\s+categories)\b/i';
    
    // Directories to search
    $directories = [
        __DIR__,
        __DIR__ . '/data',
        __DIR__ . '/../php'
    ];
    
    foreach ($directories as $dir) {
        if (!is_dir($dir)) continue;
        
        $files = glob($dir . '/*.php');
        foreach ($files as $file) {
            $matches = searchInFile($file, $pattern);
            if (!empty($matches)) {
                $results[basename($file)] = [
                    'path' => $file,
                    'matches' => $matches
                ];
            }
        }
    }
}

// Include header
include __DIR__ . '/inc/header.php';
?>

<div class="dashboard-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="welcome-header">
            <i class="fa fa-search mr-3"></i>Find 'categories' Table References
        </h1>
        <a href="index.php" class="btn btn-secondary">
            <i class="fa fa-arrow-left mr-2"></i>Kembali ke Dashboard
        </a>
    </div>

    <?php if (!empty($results)): ?>
    <div class="alert alert-warning">
        <i class="fa fa-exclamation-triangle mr-2"></i>
        <strong>File yang Masih Menggunakan Tabel 'categories':</strong>
        <div class="mt-3">
            <?php foreach ($results as $filename => $data): ?>
            <div class="card mb-3">
                <div class="card-header">
                    <strong><?= htmlspecialchars($filename) ?></strong>
                    <small class="text-muted"><?= htmlspecialchars($data['path']) ?></small>
                </div>
                <div class="card-body">
                    <?php foreach ($data['matches'] as $match): ?>
                    <div class="mb-2">
                        <span class="badge badge-warning">Line <?= $match['line'] ?></span>
                        <code><?= htmlspecialchars($match['content']) ?></code>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php elseif ($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
    <div class="alert alert-success">
        <i class="fa fa-check-circle mr-2"></i>
        <strong>Bagus!</strong> Tidak ditemukan file yang menggunakan tabel 'categories'.
        Semua file sudah menggunakan tabel 'category' yang benar.
    </div>
    <?php endif; ?>

    <div class="card summary-card">
        <div class="card-header card-header-brand-warning">
            <i class="fa fa-search mr-2"></i>Search for 'categories' Table References
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fa fa-info-circle mr-2"></i>
                <strong>Tentang Tool Ini:</strong>
                <p class="mb-0 mt-2">
                    Tool ini akan mencari semua file PHP yang masih menggunakan nama tabel 'categories' 
                    padahal database Anda menggunakan tabel 'category'. File yang ditemukan perlu diperbaiki.
                </p>
            </div>

            <form method="POST">
                <div class="text-center">
                    <h5>Cari Referensi Tabel 'categories'</h5>
                    <p class="text-muted">Klik tombol di bawah untuk mencari file yang masih menggunakan tabel 'categories'.</p>
                    
                    <button type="submit" name="search_files" class="btn btn-brand-warning btn-lg">
                        <i class="fa fa-search mr-2"></i>Cari File Bermasalah
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Quick Fix Guide -->
    <div class="card summary-card mt-4">
        <div class="card-header card-header-brand-info">
            <i class="fa fa-wrench mr-2"></i>Panduan Perbaikan
        </div>
        <div class="card-body">
            <h6>Jika ditemukan file yang menggunakan 'categories', lakukan perbaikan berikut:</h6>
            
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-danger">❌ Yang Salah:</h6>
                    <pre><code>FROM categories
INTO categories  
UPDATE categories
categories WHERE</code></pre>
                </div>
                <div class="col-md-6">
                    <h6 class="text-success">✅ Yang Benar:</h6>
                    <pre><code>FROM category
INTO category
UPDATE category  
category WHERE</code></pre>
                </div>
            </div>
            
            <div class="alert alert-warning mt-3">
                <strong>Catatan:</strong> Pastikan untuk mengubah semua referensi tabel 'categories' menjadi 'category' 
                agar sesuai dengan struktur database Anda.
            </div>
        </div>
    </div>

    <!-- Database Info -->
    <div class="card summary-card mt-4">
        <div class="card-header card-header-brand-primary">
            <i class="fa fa-database mr-2"></i>Informasi Database Anda
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Tabel yang Benar:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fa fa-check text-success mr-2"></i><code>post</code> (bukan posts)</li>
                        <li><i class="fa fa-check text-success mr-2"></i><code>category</code> (bukan categories)</li>
                        <li><i class="fa fa-check text-success mr-2"></i><code>comment</code> (bukan comments)</li>
                        <li><i class="fa fa-check text-success mr-2"></i><code>pengurus</code></li>
                        <li><i class="fa fa-check text-success mr-2"></i><code>users</code></li>
                        <li><i class="fa fa-check text-success mr-2"></i><code>jabatan</code></li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>File yang Sudah Diperbaiki:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fa fa-check text-success mr-2"></i>admin/index.php</li>
                        <li><i class="fa fa-check text-success mr-2"></i>admin/ajax/get-stats.php</li>
                        <li><i class="fa fa-check text-success mr-2"></i>admin/post-add.php</li>
                        <li><i class="fa fa-check text-success mr-2"></i>admin/post-edit.php</li>
                        <li><i class="fa fa-check text-success mr-2"></i>admin/category-add.php</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="../js/jquery-1.12.4.min.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script>
$(document).ready(function() {
    // Auto hide alerts after 15 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 15000);
});
</script>

</body>
</html>
