<?php
include_once __DIR__ . '/../../db_conn.php';

function getAllCategories($conn) {
    $stmt = $conn->query("SELECT * FROM category ORDER BY id DESC");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getCategoryById($conn, $id) {
    $stmt = $conn->prepare("SELECT * FROM category WHERE id = ? LIMIT 1");
    $stmt->execute([$id]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function get5Categories($conn) {
    $stmt = $conn->query("SELECT * FROM category ORDER BY id DESC LIMIT 5");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function get5Categoies($conn) {
    return get5Categories($conn);
}
