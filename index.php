<?php
session_start();
$logged = false;
if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
    $logged = true;
    $user_id = $_SESSION['user_id'];
}
include_once("admin/data/post.php");
include_once("db_conn.php");
// Gunakan getAllPosts jika getAllDeep tidak ada
$posts = function_exists('getAllPosts') ? getAllPosts($conn) : [];
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>UKM Panahan Universitas Semarang</title>
    <link rel="icon" href="img/logo.jpg" type="Image/x-icon">
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="description" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <style>
body {
    background: #fff !important;
    min-height: 100vh;
}
.preloader .rubix-cube .layer-1 { background: #111; }
.preloader .rubix-cube .layer-2 { background: #005a99; }
.preloader .rubix-cube .layer-3 { background: #ff9800; }
.preloader .rubix-cube .layer-4 { background: #111; }
.preloader .rubix-cube .layer-5 { background: #005a99; }
.preloader .rubix-cube .layer-6 { background: #ff9800; }
.preloader .rubix-cube .layer-7 { background: #005a99; }
.preloader .rubix-cube .layer-8 { background: #111; }
#slider-part .slider-cont {
    background: rgba(0,90,153,0.72);
    border-radius: 16px;
    padding: 36px 36px 30px 36px;
    color: #fff;
    box-shadow: 0 2px 12px 0 rgba(10,41,71,0.10);
    text-align: left;
    max-width: 540px;
}
#slider-part .slider-cont .main-btn {
    background: linear-gradient(90deg, #ff9800 0%, #005a99 100%);
    color: #fff !important;
    border: none;
    border-radius: 24px;
    padding: 10px 32px;
    font-size: 1.1rem;
    font-weight: 600;
    box-shadow: 0 2px 8px 0 rgba(10,41,71,0.10);
    transition: background 0.3s, color 0.3s, box-shadow 0.3s;
    text-shadow: 0 1px 4px rgba(10,41,71,0.10);
}
#slider-part .slider-cont .main-btn:hover, #slider-part .slider-cont .main-btn:focus {
    background: linear-gradient(90deg, #005a99 0%, #ff9800 100%);
    color: #fff !important;
    box-shadow: 0 4px 16px 0 rgba(255,152,0,0.18);
}
#slider-part .slider-cont ul li {
    display: inline-block;
    margin-right: 10px;
}
#slider-part .slider-cont ul li:last-child {
    margin-right: 0;
}
#slider-part .slider-cont h1[data-animation] {
    animation-duration: 1.2s;
    animation-fill-mode: both;
}
#slider-part .slider-cont h1[data-animation="bounceInLeft"] {
    animation-name: bounceInLeft;
}
#slider-part .slider-cont h1[data-animation="fadeInUp"] {
    animation-name: fadeInUp;
}
@keyframes bounceInLeft {
    0% { opacity: 0; transform: translateX(-2000px); }
    60% { opacity: 1; transform: translateX(30px); }
    80% { transform: translateX(-10px); }
    100% { transform: translateX(0); }
}
@keyframes fadeInUp {
    0% { opacity: 0; transform: translateY(40px); }
    100% { opacity: 1; transform: translateY(0); }
}
#patnar-logo {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    padding-top: 10px !important;
    padding-bottom: 40px !important;
    box-shadow: 0 2px 12px 0 rgba(10,41,71,0.08);
}
.patnar-slider .singel-patnar {
    margin-top: 20px !important;
    margin-bottom: 0px !important;
    padding: 0 10px !important;
}
.patnar-slider .singel-patnar img {
    max-height: 110px;
    width: auto;
    margin: 0 auto;
    filter: drop-shadow(0 2px 8px rgba(10,41,71,0.10));
    display: block;
}
.patnar-slider {
    display: flex;
    align-items: center;
}
.slick-slide {
    display: flex !important;
    justify-content: center;
    align-items: center;
}


/* Responsive header/navbar */
@media (max-width: 991px) {
    .navbar-nav { flex-direction: column !important; align-items: flex-start !important; }
    .navbar-nav .nav-item { margin-left: 0 !important; margin-bottom: 8px; }
    .navbar-brand img { max-width: 120px; height: auto; }
    footer, .footer { text-align: center !important; padding: 18px 6px !important; font-size: 0.98rem; }
}
@media (max-width: 767px) {
    .navbar-brand img { max-width: 90px; }
}
@media (max-width: 575px) {
    .navbar-brand img { max-width: 70px; }
    .footer, footer { font-size: 0.92rem; padding: 12px 2px !important; }
}
.apply {
    margin-top: 24px;
    margin-bottom: 24px;
}
.apply-cont {
    background: #fff;
    border-radius: 1.2rem;
    box-shadow: 0 4px 24px 0 rgba(46, 54, 80, 0.10);
    padding: 32px 24px 24px 24px;
    margin-bottom: 18px;
    min-height: 220px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    transition: box-shadow 0.2s, transform 0.2s;
}
.apply-cont h3 {
    color: #005a99;
    font-weight: 700;
    font-size: 1.3rem;
    margin-bottom: 12px;
    letter-spacing: 1px;
}
.apply-cont.apply-color-1 {
    background: linear-gradient(90deg, #111 0%, #005a99 100%) !important;
    color: #fff;
}
.apply-cont.apply-color-2 {
    background: linear-gradient(90deg, #005a99 0%, #ff9800 100%) !important;
    color: #fff;
}
.apply-cont p {
    font-size: 1.05rem;
    margin-bottom: 0;
    color: #fff;
    font-weight: 400;
}
@media (max-width: 991px) {
    .apply-cont {
        padding: 18px 10px 14px 10px;
        min-height: 140px;
        font-size: 0.98rem;
    }
    .apply-cont h3 {
        font-size: 1.1rem;
    }
}
@media (max-width: 575px) {
    .apply-cont {
        border-radius: 0.7rem;
        padding: 10px 4px 8px 4px;
        min-height: 90px;
    }
    .apply-cont h3 {
        font-size: 1rem;
    }
}
</style>
</head>

<body>
    <?php
    include 'inc/navbar.php';
    ?>
     <div class="preloader">
        <div class="loader rubix-cube">
            <div class="layer layer-1"></div>
            <div class="layer layer-2"></div>
            <div class="layer layer-3 color-1"></div>
            <div class="layer layer-4"></div>
            <div class="layer layer-5"></div>
            <div class="layer layer-6"></div>
            <div class="layer layer-7"></div>
            <div class="layer layer-8"></div>
        </div>
    </div>
  <section id="slider-part" class="slider-active"">
    <div class="single-slider bg_cover pt-150 pb-150" style="background-image: url(upload/header-1.jpg)" data-overlay="4">
        <div class="container">
            <div class="row">
                <div class="col-xl-7 col-lg-9">
                    <div class="slider-cont">
                        <h1 data-animation="bounceInLeft" data-delay="1s">Selamat Datang Di</h1>
                        <h1 data-animation="fadeInUp" data-delay="1.3s">Website UKM Panahan Universitas Semarang</h1>
                        <ul>
                            <li><a data-animation="fadeInUp" data-delay="1.6s" class="main-btn" href="index.php">Baca Selengkapnya</a></li>
                        </ul>
                    </div>
                </div>
            </div> <!-- row -->
        </div> <!-- container -->
    </div> <!-- single slider -->
    
    <div class="single-slider bg_cover pt-150 pb-150" style="background-image: url(upload/goal-background-img.jpg)" data-overlay="4">
        <div class="container">
            <div class="row">
                <div class="col-xl-7 col-lg-9">
                    <div class="slider-cont">
                        <h1 data-animation="bounceInLeft" data-delay="1s">Pendaftaran Mahasiswa Baru</h1>
                        <h1 data-animation="fadeInUp" data-delay="1.3s">Universitas Semarang</h1>
                        <ul>
                            <li><a data-animation="fadeInUp" data-delay="1.6s" class="main-btn" href="https://pmb.usm.ac.id/">Baca Selengkapnya</a></li>
                        </ul>
                    </div>
                </div>
            </div> <!-- row -->
        </div> <!-- container -->
    </div> <!-- single slider -->
    
    <div class="single-slider bg_cover pt-150 pb-150" style="background-image: url(upload/warta.jpg)" data-overlay="4">
        <div class="container">
            <div class="row">
                <div class="col-xl-7 col-lg-9">
                    <div class="slider-cont">
                        <h1 data-animation="bounceInLeft" data-delay="1s">Wisuda USM Ke-72</h1>
                        <h1 data-animation="fadeInUp" data-delay="1.3s">Meluluskan 694 Wisudawan</h1>
                        <ul>
                            <li><a data-animation="fadeInUp" data-delay="1.6s" class="main-btn" href="https://usm.ac.id/id/portfolio/warta-usm-tribun-jateng-28-mei-2025/">Baca Selengkapnya</a></li>
                        </ul>
                    </div>
                </div>
            </div> <!-- row -->
        </div> <!-- container -->
    </div> <!-- single slider -->
</section>
<style>
#slider-part.slider-active {
    margin-top: 0;
    min-height: 420px;
    position: relative;
    z-index: 1;
    box-shadow: 0 8px 32px 0 rgba(10,41,71,0.12);
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}
#slider-part .single-slider {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 420px;
    display: flex;
    align-items: center;
    position: relative;
    border-radius: 18px;
    box-shadow: 0 4px 24px 0 rgba(10,41,71,0.10);
    overflow: hidden;
    margin-bottom: 0px;
    border: 2px solid #fff2;
}
#slider-part .slider-cont {
    background: rgba(10,41,71,0.72);
    border-radius: 16px;
    padding: 36px 36px 30px 36px;
    color: #fff;
    box-shadow: 0 2px 12px 0 rgba(10,41,71,0.10);
    text-align: left;
    max-width: 540px;
}
#slider-part .slider-cont h1 {
    color: #fff;
    font-family: 'Teko', 'Roboto', sans-serif;
    font-size: 2.3rem;
    font-weight: 700;
    margin-bottom: 0px;
    letter-spacing: 1px;
    text-shadow: 0 2px 8px rgba(10,41,71,0.18);
}
#slider-part .slider-cont ul {
    margin-top: 18px;
    padding-left: 0;
}
#slider-part .slider-cont .main-btn {
    background: linear-gradient(90deg, #ff9800 0%, #005a99 100%);
    color: #fff !important;
    border: none;
    border-radius: 24px;
    padding: 10px 32px;
    font-size: 1.1rem;
    font-weight: 600;
    box-shadow: 0 2px 8px 0 rgba(10,41,71,0.10);
    transition: background 0.3s, color 0.3s, box-shadow 0.3s;
    text-shadow: 0 1px 4px rgba(10,41,71,0.10);
}
#slider-part .slider-cont .main-btn:hover, #slider-part .slider-cont .main-btn:focus {
    background: linear-gradient(90deg, #005a99 0%, #ff9800 100%);
    color: #fff !important;
    box-shadow: 0 4px 16px 0 rgba(255,152,0,0.18);
}
#slider-part .slider-cont ul li {
    display: inline-block;
    margin-right: 10px;
}
#slider-part .slider-cont ul li:last-child {
    margin-right: 0;
}
#slider-part .slider-cont h1[data-animation] {
    animation-duration: 1.2s;
    animation-fill-mode: both;
}
#slider-part .slider-cont h1[data-animation="bounceInLeft"] {
    animation-name: bounceInLeft;
}
#slider-part .slider-cont h1[data-animation="fadeInUp"] {
    animation-name: fadeInUp;
}
@keyframes bounceInLeft {
    0% { opacity: 0; transform: translateX(-2000px); }
    60% { opacity: 1; transform: translateX(30px); }
    80% { transform: translateX(-10px); }
    100% { transform: translateX(0); }
}
@keyframes fadeInUp {
    0% { opacity: 0; transform: translateY(40px); }
    100% { opacity: 1; transform: translateY(0); }
}
#patnar-logo {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
    padding-top: 10px !important;
    padding-bottom: 40px !important;
    box-shadow: 0 2px 12px 0 rgba(10,41,71,0.08);
}
.patnar-slider .singel-patnar {
    margin-top: 20px !important;
    margin-bottom: 0px !important;
    padding: 0 10px !important;
}
.patnar-slider .singel-patnar img {
    max-height: 110px;
    width: auto;
    margin: 0 auto;
    filter: drop-shadow(0 2px 8px rgba(10,41,71,0.10));
    display: block;
}
.patnar-slider {
    display: flex;
    align-items: center;
}
.slick-slide {
    display: flex !important;
    justify-content: center;
    align-items: center;
}
/* --- Responsive Mobile --- */
@media (max-width: 991px) {
    #slider-part .slider-cont {
        padding: 18px 8px 14px 8px;
        max-width: 100%;
        text-align: center;
    }
    #slider-part .slider-cont h1 {
        font-size: 1.2rem;
    }
    #slider-part .single-slider {
        min-height: 250px;
        border-radius: 10px;
        margin-bottom: 0px;
        padding: 0;
    }
    #slider-part.slider-active {
        min-height: 250px;
    }
}

@media (max-width: 500px) {
   
    .patnar-slider .singel-patnar img {
        max-height: 80px;
    }

}
</style>
         <div id="patnar-logo" class="pt-20 pb-40 gray-bg" style="padding-left:0;padding-right:0;">
    <div class="container-fluid px-0">
        <div class="patnar-slider">
            <div class="singel-patnar text-center mt-20">
                <img src="images/patnar-logo/p-1.png" alt="Logo">
            </div>
            <div class="singel-patnar text-center mt-20">
                <img src="images/patnar-logo/p-2.png" alt="Logo">
            </div>
            <div class="singel-patnar text-center mt-20">
                <img src="images/patnar-logo/p-3.png" alt="Logo">
            </div>
            <div class="singel-patnar text-center mt-20">
                <img src="images/patnar-logo/p-4.png" alt="Logo">
            </div>
            <div class="singel-patnar text-center mt-20">
                <img src="images/patnar-logo/p-2.png" alt="Logo">
            </div>
            <div class="singel-patnar text-center mt-20">
                <img src="images/patnar-logo/p-3.png" alt="Logo">
            </div>
        </div> <!-- patnar-slider -->
    </div> <!-- container-fluid -->
</div>
<style>


</style>

    <br>
    <!-- About Start -->
       <section id="about-part" class="pt-65">
        <div class="container">
            <div class="row">
                <div class="col-lg-5">
                    <div class="section-title mt-50">
                        <h5>KENALIN KAMI !!</h5>
                        <h2>Bersama Gendewa Geni mari raih potensi memanahmu !!</h2><br>
                        <p class="mb-4">Kami memiliki Visi dan Misi yang bertujuan mengenalkan olahraga panahan agar
                            dapat dijangkau oleh masyarakat luas.</p>
                    </div> <!-- section title -->
                   
                </div> <!-- about cont -->
                
            </div> <!-- row -->
        </div> <!-- container -->
        <div class="about-bg">
            <img src="upload/about-2.png" alt="About">
        </div><br><br><br><br><br>
        <div class="container">
            <div class="apply">
                <div class="row no-gutters">
                    <div class="col-lg-6">
                        <div class="apply-cont apply-color-1">
                            <h3>VISI</h3>
                            <p>Menjadi UKM Panahan yang unggul dan berprestasi di
                                                    tingkat nasional, serta menjadi wadah pengembangan karakter dan
                                                    sportivitas bagi mahasiswa Universitas Semarang.</p>
                       
                        </div> <!-- apply cont -->
                    </div>
                    <div class="col-lg-6">
                        <div class="apply-cont apply-color-2">
                            <h3>MISI</h3>
                            <p>1. Meningkatkan kualitas dan prestasi atlet panahan
                                melalui program latihan, serta mengikuti berbagai kompetisi.<br>
                                2. Membangun karakter dan sportivitas mahasiswa melalui kegiatan
                                latihan, pembinaan, dan pelatihan.<br>
                                3. Meningkatkan peran UKM Panahan sebagai wadah pengembangan diri
                                dan bakat mahasiswa melalui program-program pengembangan diri dan
                                pengabdian masyarakat.</p>
                        </div> <!-- apply cont -->
                    </div> 
                </div>
            </div> <!-- row -->
        </div>
    </section>
    

<script>
$(document).ready(function(){
    $('.patnar-slider').slick({
        slidesToShow: 4,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 0,
        speed: 3000,
        cssEase: 'linear',
        infinite: true,
        arrows: false,
        dots: false,
        pauseOnHover: false,
        responsive: [
            { breakpoint: 992, settings: { slidesToShow: 3 } },
            { breakpoint: 768, settings: { slidesToShow: 2 } },
            { breakpoint: 480, settings: { slidesToShow: 1 } }
        ]
    });
});
</script>
    
    <!-- About End -->

    <!-- JavaScript Libraries -->
 
    <?php   include 'inc/footer.php'; ?>
</body>

</html>