<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header('Location: ../login.php');
    exit();
}

// Include database connection
include_once __DIR__ . '/../db_conn.php';

$errors = [];
$success = false;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nama_jabatan = trim($_POST['nama_jabatan'] ?? '');
    $deskripsi = trim($_POST['deskripsi'] ?? '');
    $urutan = intval($_POST['urutan'] ?? 0);
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    
    // Validation
    if (empty($nama_jabatan)) {
        $errors[] = 'Nama jabatan tidak boleh kosong';
    } elseif (strlen($nama_jabatan) < 2) {
        $errors[] = 'Nama jabatan minimal 2 karakter';
    } elseif (strlen($nama_jabatan) > 50) {
        $errors[] = 'Nama jabatan maksimal 50 karakter';
    }
    
    if (!empty($deskripsi) && strlen($deskripsi) > 255) {
        $errors[] = 'Deskripsi maksimal 255 karakter';
    }
    
    if ($urutan < 0 || $urutan > 999) {
        $errors[] = 'Urutan harus antara 0-999';
    }
    
    // Check if jabatan name already exists
    if (empty($errors)) {
        try {
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM jabatan WHERE nama_jabatan = ?");
            $stmt->execute([$nama_jabatan]);
            $count = $stmt->fetchColumn();
            
            if ($count > 0) {
                $errors[] = 'Nama jabatan sudah ada, gunakan nama yang berbeda';
            }
        } catch (Exception $e) {
            $errors[] = 'Error checking jabatan: ' . $e->getMessage();
        }
    }
    
    // Insert to database
    if (empty($errors)) {
        try {
            $stmt = $conn->prepare("INSERT INTO jabatan (nama_jabatan, deskripsi, urutan, is_active, created_at) VALUES (?, ?, ?, ?, NOW())");
            $result = $stmt->execute([$nama_jabatan, $deskripsi, $urutan, $is_active]);
            
            if ($result) {
                header('Location: jabatan.php?success=' . urlencode('Jabatan berhasil ditambahkan'));
                exit();
            } else {
                $errors[] = 'Gagal menambahkan jabatan ke database';
            }
        } catch (Exception $e) {
            $errors[] = 'Error: ' . $e->getMessage();
        }
    }
}

// Include header setelah semua redirect logic
include __DIR__ . '/inc/header.php';
?>

<div class="dashboard-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="welcome-header">
            <i class="fa fa-plus mr-3"></i>Tambah Jabatan Baru
        </h1>
        <a href="jabatan.php" class="btn btn-secondary">
            <i class="fa fa-arrow-left mr-2"></i>Kembali
        </a>
    </div>

    <?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <i class="fa fa-exclamation-circle mr-2"></i>
        <strong>Terjadi kesalahan:</strong>
        <ul class="mb-0 mt-2">
            <?php foreach ($errors as $error): ?>
            <li><?= htmlspecialchars($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php endif; ?>

    <div class="card summary-card">
        <div class="card-header card-header-brand-primary">
            <i class="fa fa-briefcase mr-2"></i>Form Tambah Jabatan
        </div>
        <div class="card-body">
            <form method="POST" id="jabatanForm">
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group">
                            <label for="nama_jabatan"><i class="fa fa-briefcase mr-1"></i>Nama Jabatan *</label>
                            <input type="text" class="form-control" id="nama_jabatan" name="nama_jabatan" 
                                   value="<?= htmlspecialchars($_POST['nama_jabatan'] ?? '') ?>" 
                                   placeholder="Contoh: Ketua, Wakil Ketua, Sekretaris" required>
                            <small class="form-text text-muted">Nama jabatan yang akan ditampilkan (2-50 karakter)</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="urutan"><i class="fa fa-sort-numeric-asc mr-1"></i>Urutan Tampil</label>
                            <input type="number" class="form-control" id="urutan" name="urutan" 
                                   value="<?= htmlspecialchars($_POST['urutan'] ?? '1') ?>" 
                                   min="0" max="999" placeholder="1">
                            <small class="form-text text-muted">Urutan tampil jabatan (0-999)</small>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="deskripsi"><i class="fa fa-info-circle mr-1"></i>Deskripsi</label>
                    <textarea class="form-control" id="deskripsi" name="deskripsi" rows="3" 
                              placeholder="Deskripsi singkat tentang jabatan ini (opsional)"><?= htmlspecialchars($_POST['deskripsi'] ?? '') ?></textarea>
                    <small class="form-text text-muted">Deskripsi jabatan maksimal 255 karakter (opsional)</small>
                </div>
                
                <div class="form-group">
                    <div class="custom-control custom-switch">
                        <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" 
                               <?= isset($_POST['is_active']) ? 'checked' : 'checked' ?>>
                        <label class="custom-control-label" for="is_active">
                            <i class="fa fa-check-circle mr-1"></i>Jabatan Aktif
                        </label>
                    </div>
                    <small class="form-text text-muted">Centang jika jabatan ini aktif dan dapat digunakan</small>
                </div>
                
                <hr>
                
                <div class="form-group mb-0">
                    <button type="submit" class="btn btn-brand-primary">
                        <i class="fa fa-save mr-2"></i>Simpan Jabatan
                    </button>
                    <a href="jabatan.php" class="btn btn-secondary ml-2">
                        <i class="fa fa-times mr-2"></i>Batal
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <!-- Preview Card -->
    <div class="card summary-card mt-4">
        <div class="card-header card-header-brand-info">
            <i class="fa fa-eye mr-2"></i>Preview Jabatan
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h5 id="preview-nama">-</h5>
                    <p id="preview-deskripsi" class="text-muted">-</p>
                </div>
                <div class="col-md-6">
                    <small class="text-muted">
                        <strong>Urutan:</strong> <span id="preview-urutan">-</span><br>
                        <strong>Status:</strong> <span id="preview-status">-</span>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="../js/jquery-1.12.4.min.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script>
$(document).ready(function() {
    // Real-time preview
    function updatePreview() {
        var nama = $('#nama_jabatan').val() || '-';
        var deskripsi = $('#deskripsi').val() || '-';
        var urutan = $('#urutan').val() || '-';
        var status = $('#is_active').is(':checked') ? 'Aktif' : 'Tidak Aktif';

        $('#preview-nama').text(nama);
        $('#preview-deskripsi').text(deskripsi);
        $('#preview-urutan').text(urutan);
        $('#preview-status').text(status).removeClass('text-success text-secondary')
                           .addClass($('#is_active').is(':checked') ? 'text-success' : 'text-secondary');
    }

    // Update preview on input change
    $('#nama_jabatan, #deskripsi, #urutan').on('input', updatePreview);
    $('#is_active').on('change', updatePreview);

    // Initial preview
    updatePreview();

    // Form validation
    $('#jabatanForm').on('submit', function(e) {
        var nama = $('#nama_jabatan').val().trim();
        var urutan = parseInt($('#urutan').val());

        if (nama.length < 2) {
            e.preventDefault();
            alert('Nama jabatan minimal 2 karakter');
            $('#nama_jabatan').focus();
            return false;
        }

        if (nama.length > 50) {
            e.preventDefault();
            alert('Nama jabatan maksimal 50 karakter');
            $('#nama_jabatan').focus();
            return false;
        }

        if (urutan < 0 || urutan > 999) {
            e.preventDefault();
            alert('Urutan harus antara 0-999');
            $('#urutan').focus();
            return false;
        }
    });

    // Character counter for deskripsi
    $('#deskripsi').on('input', function() {
        var length = $(this).val().length;
        var maxLength = 255;
        var remaining = maxLength - length;

        if (!$(this).next('.char-counter').length) {
            $(this).after('<small class="char-counter form-text text-muted"></small>');
        }

        $(this).next('.char-counter').text(remaining + ' karakter tersisa')
               .removeClass('text-danger text-warning')
               .addClass(remaining < 20 ? 'text-danger' : (remaining < 50 ? 'text-warning' : ''));
    });
});
</script>

</body>
</html>
