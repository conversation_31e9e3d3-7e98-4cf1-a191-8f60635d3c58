<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header('Location: ../login.php');
    exit();
}

// Include database connection
include_once __DIR__ . '/../db_conn.php';

$messages = [];
$errors = [];

// Function to check if table exists
function tableExists($conn, $tableName) {
    try {
        $stmt = $conn->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$tableName]);
        return $stmt->rowCount() > 0;
    } catch (Exception $e) {
        return false;
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['check_tables'])) {
    
    // Check which tables exist
    $tables_to_check = [
        'post' => 'Tabel Post',
        'posts' => 'Tabel Posts', 
        'category' => 'Tabel Category',
        'categories' => 'Tabel Categories',
        'comment' => 'Tabel Comment',
        'comments' => 'Tabel Comments',
        'pengurus' => 'Tabel Pengurus',
        'users' => 'Tabel Users',
        'jabatan' => 'Tabel Jabatan'
    ];
    
    foreach ($tables_to_check as $table => $description) {
        if (tableExists($conn, $table)) {
            $messages[] = "✅ $description ($table) - Ada";
            
            // Get row count
            try {
                $stmt = $conn->query("SELECT COUNT(*) as count FROM `$table`");
                $count = $stmt->fetchColumn();
                $messages[] = "   📊 Jumlah data: $count record";
            } catch (Exception $e) {
                $messages[] = "   ⚠️ Error counting: " . $e->getMessage();
            }
        } else {
            $errors[] = "❌ $description ($table) - Tidak ada";
        }
    }
}

// Include header
include __DIR__ . '/inc/header.php';
?>

<div class="dashboard-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="welcome-header">
            <i class="fa fa-database mr-3"></i>Check Database Tables
        </h1>
        <a href="index.php" class="btn btn-secondary">
            <i class="fa fa-arrow-left mr-2"></i>Kembali ke Dashboard
        </a>
    </div>

    <?php if (!empty($messages)): ?>
    <div class="alert alert-success">
        <i class="fa fa-check-circle mr-2"></i>
        <strong>Tabel yang Ditemukan:</strong>
        <ul class="mb-0 mt-2">
            <?php foreach ($messages as $message): ?>
            <li><?= htmlspecialchars($message) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php endif; ?>

    <?php if (!empty($errors)): ?>
    <div class="alert alert-warning">
        <i class="fa fa-exclamation-triangle mr-2"></i>
        <strong>Tabel yang Tidak Ditemukan:</strong>
        <ul class="mb-0 mt-2">
            <?php foreach ($errors as $error): ?>
            <li><?= htmlspecialchars($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php endif; ?>

    <div class="card summary-card">
        <div class="card-header card-header-brand-info">
            <i class="fa fa-search mr-2"></i>Database Table Checker
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fa fa-info-circle mr-2"></i>
                <strong>Tentang Tool Ini:</strong>
                <p class="mb-0 mt-2">
                    Tool ini akan memeriksa tabel mana yang ada di database Anda untuk memastikan 
                    admin panel menggunakan nama tabel yang benar.
                </p>
            </div>

            <form method="POST">
                <div class="text-center">
                    <h5>Periksa Tabel Database</h5>
                    <p class="text-muted">Klik tombol di bawah untuk memeriksa tabel yang ada di database.</p>
                    
                    <button type="submit" name="check_tables" class="btn btn-brand-info btn-lg">
                        <i class="fa fa-search mr-2"></i>Periksa Tabel Database
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Database Info -->
    <div class="card summary-card mt-4">
        <div class="card-header card-header-brand-primary">
            <i class="fa fa-info-circle mr-2"></i>Informasi Database
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Tabel yang Dicari:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fa fa-table mr-2"></i><code>post</code> atau <code>posts</code></li>
                        <li><i class="fa fa-table mr-2"></i><code>category</code> atau <code>categories</code></li>
                        <li><i class="fa fa-table mr-2"></i><code>comment</code> atau <code>comments</code></li>
                        <li><i class="fa fa-table mr-2"></i><code>pengurus</code></li>
                        <li><i class="fa fa-table mr-2"></i><code>users</code></li>
                        <li><i class="fa fa-table mr-2"></i><code>jabatan</code></li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Database Info:</h6>
                    <?php
                    try {
                        $stmt = $conn->query("SELECT DATABASE() as db_name");
                        $db_name = $stmt->fetchColumn();
                        echo '<p><strong>Database:</strong> <code>' . htmlspecialchars($db_name) . '</code></p>';
                        
                        $stmt = $conn->query("SELECT VERSION() as version");
                        $version = $stmt->fetchColumn();
                        echo '<p><strong>MySQL Version:</strong> <code>' . htmlspecialchars($version) . '</code></p>';
                        
                        $stmt = $conn->query("SELECT @@character_set_database as charset");
                        $charset = $stmt->fetchColumn();
                        echo '<p><strong>Character Set:</strong> <code>' . htmlspecialchars($charset) . '</code></p>';
                        
                    } catch (Exception $e) {
                        echo '<p class="text-danger">Error getting database info: ' . htmlspecialchars($e->getMessage()) . '</p>';
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Fix -->
    <?php if (!empty($errors)): ?>
    <div class="card summary-card mt-4">
        <div class="card-header card-header-brand-warning">
            <i class="fa fa-wrench mr-2"></i>Quick Fix
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                <i class="fa fa-exclamation-triangle mr-2"></i>
                <strong>Solusi:</strong>
                <p class="mb-2">Berdasarkan informasi Anda bahwa database menggunakan tabel <code>post</code> bukan <code>posts</code>, 
                admin panel sudah diperbaiki untuk menggunakan prioritas yang benar:</p>
                <ol class="mb-0">
                    <li>Coba tabel <code>post</code> terlebih dahulu</li>
                    <li>Fallback ke <code>posts</code> jika tidak ada</li>
                    <li>Set nilai 0 jika kedua tabel tidak ada</li>
                </ol>
            </div>
            
            <div class="text-center">
                <a href="index.php" class="btn btn-brand-primary">
                    <i class="fa fa-dashboard mr-2"></i>Test Dashboard
                </a>
                <a href="fix-collation.php" class="btn btn-brand-warning ml-2">
                    <i class="fa fa-wrench mr-2"></i>Fix Database
                </a>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<script src="../js/jquery-1.12.4.min.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script>
$(document).ready(function() {
    // Auto hide alerts after 10 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 10000);
});
</script>

</body>
</html>
