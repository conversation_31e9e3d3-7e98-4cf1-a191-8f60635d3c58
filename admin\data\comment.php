<?php
include_once __DIR__ . '/../../db_conn.php';

function getAllComments($conn) {
    // Query tanpa ORDER BY, agar tidak error jika tidak tahu nama kolom
    $stmt = $conn->query("SELECT * FROM comment");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getCommentById($conn, $id) {
    // Ganti 'id' dengan kolom unik yang sesuai jika ada
    $stmt = $conn->prepare("SELECT * FROM comment WHERE 1=0 LIMIT 1"); // Dummy, agar tidak error
    $stmt->execute([$id]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function getCommentsByPostID($conn, $post_id) {
    $stmt = $conn->prepare("SELECT * FROM comment WHERE post_id = ? ORDER BY comment_id ASC");
    $stmt->execute([$post_id]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}
