<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header('Location: ../login.php');
    exit();
}

// Include database connection
include_once __DIR__ . '/../db_conn.php';

// Include data handler
include_once 'data/pengurus.php';

// Check if ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: pengurus.php?error=' . urlencode('ID pengurus tidak valid'));
    exit();
}

$id = intval($_GET['id']);
$errors = [];

// Get jabatan list from database
try {
    $stmt = $conn->query("SELECT * FROM jabatan WHERE is_active = 1 ORDER BY urutan ASC, nama_jabatan ASC");
    $jabatan_list = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $jabatan_list = [];
    $errors[] = 'Error mengambil data jabatan: ' . $e->getMessage();
}

// Get pengurus data
try {
    $pengurus = getPengurusById($conn, $id);
    if (!$pengurus) {
        header('Location: pengurus.php?error=' . urlencode('Pengurus tidak ditemukan'));
        exit();
    }
} catch (Exception $e) {
    header('Location: pengurus.php?error=' . urlencode('Error: ' . $e->getMessage()));
    exit();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nama = trim($_POST['nama'] ?? '');
    $jabatan = trim($_POST['jabatan'] ?? '');
    $instagram = trim($_POST['instagram'] ?? '');

    // Validation
    if (empty($nama)) {
        $errors[] = 'Nama tidak boleh kosong';
    }

    if (empty($jabatan)) {
        $errors[] = 'Jabatan tidak boleh kosong';
    }

    if (!empty($instagram) && !filter_var($instagram, FILTER_VALIDATE_URL)) {
        $errors[] = 'Link Instagram tidak valid';
    }

    // Handle file upload
    $fotoName = $pengurus['foto']; // Keep existing photo by default
    if (isset($_FILES['foto']) && $_FILES['foto']['error'] === UPLOAD_ERR_OK) {
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $maxSize = 5 * 1024 * 1024; // 5MB

        if (!in_array($_FILES['foto']['type'], $allowedTypes)) {
            $errors[] = 'Tipe file tidak diizinkan. Gunakan JPG, PNG, GIF, atau WebP';
        } elseif ($_FILES['foto']['size'] > $maxSize) {
            $errors[] = 'Ukuran file terlalu besar. Maksimal 5MB';
        } else {
            $uploadDir = '../upload/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            // Delete old photo if exists
            if ($pengurus['foto'] && file_exists($uploadDir . $pengurus['foto'])) {
                unlink($uploadDir . $pengurus['foto']);
            }

            $fileExtension = pathinfo($_FILES['foto']['name'], PATHINFO_EXTENSION);
            $fotoName = 'pengurus_' . time() . '_' . uniqid() . '.' . $fileExtension;

            if (!move_uploaded_file($_FILES['foto']['tmp_name'], $uploadDir . $fotoName)) {
                $errors[] = 'Gagal mengupload foto';
            }
        }
    }

    // Update database
    if (empty($errors)) {
        try {
            $stmt = $conn->prepare("UPDATE pengurus SET nama = ?, jabatan = ?, foto = ?, instagram = ? WHERE id = ?");
            $result = $stmt->execute([$nama, $jabatan, $fotoName, $instagram, $id]);

            if ($result) {
                header('Location: pengurus.php?success=' . urlencode('Pengurus berhasil diupdate'));
                exit();
            } else {
                $errors[] = 'Gagal mengupdate pengurus';
            }
        } catch (Exception $e) {
            $errors[] = 'Error: ' . $e->getMessage();
        }
    }
}

// Include header setelah semua redirect logic
include __DIR__ . '/inc/header.php';
?>

<div class="dashboard-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="welcome-header">
            <i class="fa fa-edit mr-3"></i>Edit Pengurus
        </h1>
        <a href="pengurus.php" class="btn btn-secondary">
            <i class="fa fa-arrow-left mr-2"></i>Kembali
        </a>
    </div>

    <?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <i class="fa fa-exclamation-circle mr-2"></i>
        <ul class="mb-0">
            <?php foreach ($errors as $error): ?>
            <li><?= htmlspecialchars($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php endif; ?>

    <div class="card summary-card">
        <div class="card-header card-header-brand-primary">
            <i class="fa fa-edit mr-2"></i>Form Edit Pengurus
        </div>
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="nama"><i class="fa fa-user mr-1"></i>Nama Lengkap *</label>
                            <input type="text" class="form-control" id="nama" name="nama"
                                   value="<?= htmlspecialchars($pengurus['nama'] ?? '') ?>"
                                   placeholder="Masukkan nama lengkap" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="jabatan"><i class="fa fa-briefcase mr-1"></i>Jabatan *</label>
                            <select class="form-control" id="jabatan" name="jabatan" required>
                                <option value="">Pilih Jabatan</option>
                                <?php if (!empty($jabatan_list)): ?>
                                    <?php foreach ($jabatan_list as $jab): ?>
                                    <option value="<?= htmlspecialchars($jab['nama_jabatan']) ?>"
                                            <?= ($pengurus['jabatan'] ?? '') == $jab['nama_jabatan'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($jab['nama_jabatan']) ?>
                                        <?php if (!empty($jab['deskripsi'])): ?>
                                        - <?= htmlspecialchars(substr($jab['deskripsi'], 0, 30)) ?>
                                        <?php endif; ?>
                                    </option>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <option value="Ketua" <?= ($pengurus['jabatan'] ?? '') == 'Ketua' ? 'selected' : '' ?>>Ketua</option>
                                    <option value="Wakil Ketua" <?= ($pengurus['jabatan'] ?? '') == 'Wakil Ketua' ? 'selected' : '' ?>>Wakil Ketua</option>
                                    <option value="Sekretaris" <?= ($pengurus['jabatan'] ?? '') == 'Sekretaris' ? 'selected' : '' ?>>Sekretaris</option>
                                    <option value="Bendahara" <?= ($pengurus['jabatan'] ?? '') == 'Bendahara' ? 'selected' : '' ?>>Bendahara</option>
                                    <option value="Koordinator Divisi" <?= ($pengurus['jabatan'] ?? '') == 'Koordinator Divisi' ? 'selected' : '' ?>>Koordinator Divisi</option>
                                    <option value="Anggota" <?= ($pengurus['jabatan'] ?? '') == 'Anggota' ? 'selected' : '' ?>>Anggota</option>
                                <?php endif; ?>
                            </select>
                            <small class="form-text text-muted">
                                Pilih jabatan dari daftar yang tersedia.
                                <a href="jabatan.php" target="_blank" class="text-primary">Kelola jabatan</a>
                            </small>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="instagram"><i class="fa fa-instagram mr-1"></i>Link Instagram</label>
                    <input type="url" class="form-control" id="instagram" name="instagram"
                           value="<?= htmlspecialchars($pengurus['instagram'] ?? '') ?>"
                           placeholder="https://instagram.com/username">
                    <small class="form-text text-muted">Link Instagram pengurus (opsional)</small>
                </div>

                <div class="form-group">
                    <label for="foto"><i class="fa fa-image mr-1"></i>Foto Pengurus</label>

                    <?php if (!empty($pengurus['foto'])): ?>
                    <div class="current-photo mb-3">
                        <label class="text-muted">Foto Saat Ini:</label><br>
                        <img src="../upload/<?= htmlspecialchars($pengurus['foto']) ?>"
                             alt="Foto Pengurus"
                             style="max-width: 200px; max-height: 200px; border-radius: 10px; border: 2px solid #005a99;">
                    </div>
                    <?php endif; ?>

                    <div class="custom-file">
                        <input type="file" class="custom-file-input" id="foto" name="foto" accept="image/*">
                        <label class="custom-file-label" for="foto">Pilih foto baru...</label>
                    </div>
                    <small class="form-text text-muted">
                        Format: JPG, PNG, GIF, WebP. Maksimal 5MB. Kosongkan jika tidak ingin mengubah foto.
                    </small>
                    <div id="imagePreview" class="mt-3" style="display: none;">
                        <label class="text-muted">Preview Foto Baru:</label><br>
                        <img id="preview" src="" alt="Preview" style="max-width: 200px; max-height: 200px; border-radius: 10px; border: 2px solid #005a99;">
                    </div>
                </div>

                <div class="form-group">
                    <label><i class="fa fa-info-circle mr-1"></i>Informasi Pengurus</label>
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <strong>ID Pengurus:</strong> <?= htmlspecialchars($pengurus['id']) ?><br>
                                <strong>Nama:</strong> <?= htmlspecialchars($pengurus['nama']) ?>
                            </small>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">
                                <strong>Jabatan:</strong> <?= htmlspecialchars($pengurus['jabatan']) ?><br>
                                <strong>Status:</strong> <span class="text-success">Aktif</span>
                            </small>
                        </div>
                    </div>
                </div>

                <div class="form-group text-right">
                    <a href="pengurus.php" class="btn btn-secondary mr-2">
                        <i class="fa fa-times mr-1"></i>Batal
                    </a>
                    <button type="submit" class="btn btn-brand-primary">
                        <i class="fa fa-save mr-1"></i>Update Pengurus
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="../js/jquery-1.12.4.min.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script>
$(document).ready(function() {
    // Custom file input
    $('.custom-file-input').on('change', function() {
        var fileName = $(this).val().split('\\').pop();
        $(this).next('.custom-file-label').html(fileName || 'Pilih foto baru...');

        // Image preview
        if (this.files && this.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                $('#preview').attr('src', e.target.result);
                $('#imagePreview').show();
            }
            reader.readAsDataURL(this.files[0]);
        }
    });

    // Instagram URL validation
    $('#instagram').on('blur', function() {
        var url = $(this).val();
        if (url && !url.includes('instagram.com')) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">URL harus berupa link Instagram yang valid</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    // Form validation
    $('form').on('submit', function(e) {
        var isValid = true;

        // Check required fields
        $(this).find('input[required], select[required]').each(function() {
            if (!$(this).val().trim()) {
                $(this).addClass('is-invalid');
                isValid = false;
            } else {
                $(this).removeClass('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            alert('Mohon lengkapi semua field yang wajib diisi!');
        }
    });
});
</script>

</body>
</html>
