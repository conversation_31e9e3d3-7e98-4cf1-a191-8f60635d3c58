<?php 
session_start();
$logged = false;
if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
    $logged = true;
    $user_id = $_SESSION['user_id'];
}

include_once("admin/data/post.php");
include_once("admin/data/comment.php");
include_once("admin/data/category.php");
include_once("admin/data/like.php");
include_once("admin/data/user.php");
include_once("db_conn.php");

// Pagination settings
$posts_per_page = 6;
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$offset = ($page - 1) * $posts_per_page;

// Get search query
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// Get category filter
$category_filter = isset($_GET['category']) ? intval($_GET['category']) : 0;

// Get posts based on filters
if ($search) {
    $posts = searchPosts($conn, $search);
} else {
    $posts = getAllPosts($conn);
}

// Filter by category if specified
if ($category_filter && $posts) {
    $posts = array_filter($posts, function($post) use ($category_filter) {
        return isset($post['category_id']) && $post['category_id'] == $category_filter;
    });
}

// Calculate pagination
$total_posts = count($posts);
$total_pages = ceil($total_posts / $posts_per_page);
$posts = array_slice($posts, $offset, $posts_per_page);

// Get categories for sidebar
$categories = get5Categoies($conn);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog - UKM Panahan Gendewa Geni</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
    body {
        background: #fff !important;
        min-height: 100vh;
        font-family: 'Poppins', Arial, sans-serif;
    }
    .blog-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 15px;
    }
    .blog-header {
        text-align: center;
        margin-bottom: 48px;
        padding: 48px 0;
        background: linear-gradient(135deg, #005a99 0%, #ff8800 100%);
        color: white;
        border-radius: 16px;
        margin-top: 32px;
    }
    .blog-header h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 16px;
    }
    .blog-header p {
        font-size: 1.1rem;
        opacity: 0.9;
    }
    .blog-content {
        display: flex;
        gap: 32px;
        align-items: flex-start;
    }
    .blog-main {
        flex: 1;
    }
    .blog-sidebar {
        width: 320px;
        min-width: 280px;
    }
    .blog-card {
        background: #fff;
        border-radius: 16px;
        box-shadow: 0 4px 24px rgba(0,90,153,0.1);
        margin-bottom: 32px;
        overflow: hidden;
        transition: transform 0.3s, box-shadow 0.3s;
        border: none;
    }
    .blog-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 36px rgba(0,90,153,0.15);
    }
    .blog-card img {
        width: 100%;
        height: 240px;
        object-fit: cover;
    }
    .blog-card-body {
        padding: 24px;
    }
    .blog-card-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        margin-bottom: 16px;
        align-items: center;
    }
    .blog-card-meta .badge {
        background: #005a99;
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
    }
    .blog-card-meta .text-muted {
        font-size: 0.9rem;
        color: #666;
    }
    .blog-card-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: #005a99;
        margin-bottom: 12px;
        line-height: 1.4;
    }
    .blog-card-title a {
        color: inherit;
        text-decoration: none;
    }
    .blog-card-title a:hover {
        color: #ff8800;
    }
    .blog-card-excerpt {
        color: #555;
        line-height: 1.6;
        margin-bottom: 16px;
    }
    .blog-card-footer {
        display: flex;
        justify-content: between;
        align-items: center;
        padding-top: 16px;
        border-top: 1px solid #eee;
    }
    .search-form {
        background: #fff;
        padding: 24px;
        border-radius: 16px;
        box-shadow: 0 4px 24px rgba(0,90,153,0.08);
        margin-bottom: 32px;
    }
    .category-sidebar {
        background: #fff;
        border-radius: 16px;
        box-shadow: 0 4px 24px rgba(0,90,153,0.08);
        overflow: hidden;
    }
    .category-sidebar .list-group-item {
        border: none;
        background: transparent;
        transition: background 0.2s, color 0.2s;
        font-weight: 600;
        color: #005a99;
        font-size: 1.05rem;
    }
    .category-sidebar .list-group-item.active,
    .category-sidebar .list-group-item:active {
        background: linear-gradient(90deg, #005a99 0%, #ff8800 100%);
        color: #fff;
        border: none;
        box-shadow: 0 2px 8px #005a9922;
    }
    .category-sidebar .list-group-item:not(.active):hover {
        background: #f0f4fa;
        color: #ff8800;
    }
    .pagination-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 48px;
    }
    .pagination .page-link {
        color: #005a99;
        border-color: #005a99;
    }
    .pagination .page-item.active .page-link {
        background-color: #005a99;
        border-color: #005a99;
    }
    .pagination .page-link:hover {
        color: #ff8800;
        border-color: #ff8800;
    }
    @media (max-width: 991px) {
        .blog-content {
            flex-direction: column;
            gap: 24px;
        }
        .blog-sidebar {
            width: 100%;
        }
        .blog-header h1 {
            font-size: 2rem;
        }
    }
    @media (max-width: 768px) {
        .blog-container {
            padding: 0 15px;
        }
        .blog-header {
            padding: 32px 20px;
            margin-top: 20px;
        }
        .blog-header h1 {
            font-size: 1.8rem;
        }
        .blog-card img {
            height: 200px;
        }
        .blog-card-body {
            padding: 20px;
        }
        .blog-card-title {
            font-size: 1.1rem;
        }
        .search-form {
            padding: 20px;
        }
    }
    @media (max-width: 576px) {
        .blog-container {
            padding: 0 10px;
        }
        .blog-header {
            padding: 24px 15px;
            margin-top: 15px;
        }
        .blog-header h1 {
            font-size: 1.5rem;
        }
        .blog-header p {
            font-size: 0.95rem;
        }
        .blog-card img {
            height: 180px;
        }
        .blog-card-body {
            padding: 16px;
        }
        .blog-card-title {
            font-size: 1rem;
        }
        .blog-card-meta .badge {
            font-size: 0.7rem;
            padding: 4px 8px;
        }
        .search-form {
            padding: 16px;
        }
    }
    </style>
</head>
<body>
    <?php include 'inc/NavBar.php'; ?>
    
    <div class="blog-container">
        <div class="blog-header">
            <h1><i class="fa fa-newspaper-o me-3"></i>Blog & Artikel</h1>
            <p>Temukan artikel menarik seputar dunia panahan dan kegiatan UKM Gendewa Geni</p>
        </div>
        
        <div class="blog-content">
            <main class="blog-main">
                <!-- Search Form -->
                <div class="search-form">
                    <form method="GET" action="">
                        <div class="row g-3">
                            <div class="col-md-8">
                                <input type="text" class="form-control" name="search" 
                                       placeholder="Cari artikel..." value="<?=htmlspecialchars($search)?>">
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fa fa-search me-2"></i>Cari
                                </button>
                            </div>
                        </div>
                        <?php if ($category_filter): ?>
                            <input type="hidden" name="category" value="<?=$category_filter?>">
                        <?php endif; ?>
                    </form>
                </div>
                
                <!-- Blog Posts -->
                <div class="row">
                    <?php if (!empty($posts)): ?>
                        <?php foreach ($posts as $post): ?>
                            <?php
                            // Get category name
                            $category_name = '';
                            if (isset($post['category_id']) && $post['category_id']) {
                                $category = getCategoryById($conn, $post['category_id']);
                                $category_name = $category ? $category['category'] : '';
                            }
                            
                            // Get post excerpt
                            $excerpt = strlen($post['post_text']) > 150 ? 
                                       substr($post['post_text'], 0, 150) . '...' : 
                                       $post['post_text'];
                            ?>
                            <div class="col-lg-6 col-md-6 mb-4">
                                <div class="blog-card">
                                    <img src="upload/blog/<?=$post['cover_url']?>" 
                                         alt="<?=htmlspecialchars($post['post_title'])?>">
                                    <div class="blog-card-body">
                                        <div class="blog-card-meta">
                                            <?php if ($category_name): ?>
                                                <span class="badge">
                                                    <i class="fa fa-folder-o me-1"></i><?=htmlspecialchars($category_name)?>
                                                </span>
                                            <?php endif; ?>
                                            <span class="text-muted">
                                                <i class="fa fa-calendar me-1"></i>
                                                <?=date('d M Y', strtotime($post['created_at']))?>
                                            </span>
                                        </div>
                                        <h3 class="blog-card-title">
                                            <a href="blog-view.php?post_id=<?=$post['id']?>">
                                                <?=htmlspecialchars($post['post_title'])?>
                                            </a>
                                        </h3>
                                        <p class="blog-card-excerpt">
                                            <?=htmlspecialchars($excerpt)?>
                                        </p>
                                        <div class="blog-card-footer">
                                            <a href="blog-view.php?post_id=<?=$post['id']?>" 
                                               class="btn btn-outline-primary btn-sm">
                                                <i class="fa fa-arrow-right me-1"></i>Baca Selengkapnya
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="col-12">
                            <div class="text-center py-5">
                                <i class="fa fa-search fa-3x text-muted mb-3"></i>
                                <h4 class="text-muted">Tidak ada artikel ditemukan</h4>
                                <p class="text-muted">Coba gunakan kata kunci yang berbeda atau lihat semua artikel.</p>
                                <a href="blog.php" class="btn btn-primary">Lihat Semua Artikel</a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                    <div class="pagination-wrapper">
                        <nav aria-label="Blog pagination">
                            <ul class="pagination">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?=$page-1?><?=$search ? '&search='.urlencode($search) : ''?><?=$category_filter ? '&category='.$category_filter : ''?>">
                                            <i class="fa fa-chevron-left"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?=$i == $page ? 'active' : ''?>">
                                        <a class="page-link" href="?page=<?=$i?><?=$search ? '&search='.urlencode($search) : ''?><?=$category_filter ? '&category='.$category_filter : ''?>">
                                            <?=$i?>
                                        </a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?=$page+1?><?=$search ? '&search='.urlencode($search) : ''?><?=$category_filter ? '&category='.$category_filter : ''?>">
                                            <i class="fa fa-chevron-right"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    </div>
                <?php endif; ?>
            </main>
            
            <aside class="blog-sidebar">
                <div class="category-sidebar">
                    <div class="list-group">
                        <a href="blog.php" class="list-group-item list-group-item-action <?=!$category_filter ? 'active' : ''?>">
                            <i class="fa fa-list me-2"></i>Semua Kategori
                        </a>
                        <?php foreach ($categories as $category): ?>
                            <a href="blog.php?category=<?=$category['id']?>" 
                               class="list-group-item list-group-item-action <?=$category_filter == $category['id'] ? 'active' : ''?>">
                                <i class="fa fa-folder-o me-2"></i><?=htmlspecialchars($category['category'])?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </aside>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <?php include 'inc/footer.php'; ?>
</body>
</html>
