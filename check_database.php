<?php
// Script untuk memeriksa dan memperbarui struktur database
include_once 'db_conn.php';

echo "<h2>Database Structure Check</h2>";

try {
    // Cek struktur tabel users
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Struktur Tabel Users:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $hasProfilePic = false;
    $hasRole = false;
    $hasFname = false;
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'profile_pic') $hasProfilePic = true;
        if ($column['Field'] === 'role') $hasRole = true;
        if ($column['Field'] === 'fname') $hasFname = true;
    }
    echo "</table>";
    
    // Tambahkan kolom yang hilang
    $alterQueries = [];
    
    if (!$hasProfilePic) {
        $alterQueries[] = "ALTER TABLE users ADD COLUMN profile_pic VARCHAR(255) DEFAULT NULL";
        echo "<p style='color: orange;'>⚠️ Kolom 'profile_pic' tidak ditemukan. Akan ditambahkan.</p>";
    } else {
        echo "<p style='color: green;'>✅ Kolom 'profile_pic' sudah ada.</p>";
    }
    
    if (!$hasRole) {
        $alterQueries[] = "ALTER TABLE users ADD COLUMN role VARCHAR(50) DEFAULT 'Anggota'";
        echo "<p style='color: orange;'>⚠️ Kolom 'role' tidak ditemukan. Akan ditambahkan.</p>";
    } else {
        echo "<p style='color: green;'>✅ Kolom 'role' sudah ada.</p>";
    }
    
    if (!$hasFname) {
        $alterQueries[] = "ALTER TABLE users ADD COLUMN fname VARCHAR(255) DEFAULT NULL";
        echo "<p style='color: orange;'>⚠️ Kolom 'fname' tidak ditemukan. Akan ditambahkan.</p>";
    } else {
        echo "<p style='color: green;'>✅ Kolom 'fname' sudah ada.</p>";
    }
    
    // Eksekusi query ALTER TABLE
    if (!empty($alterQueries)) {
        echo "<h3>Menambahkan Kolom yang Hilang:</h3>";
        foreach ($alterQueries as $query) {
            try {
                $conn->exec($query);
                echo "<p style='color: green;'>✅ Berhasil: " . $query . "</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>❌ Gagal: " . $query . "<br>Error: " . $e->getMessage() . "</p>";
            }
        }
    } else {
        echo "<p style='color: green;'>✅ Semua kolom yang diperlukan sudah ada.</p>";
    }
    
    // Cek data sample
    echo "<h3>Sample Data Users:</h3>";
    $stmt = $conn->query("SELECT id, username, fname, role, profile_pic FROM users LIMIT 5");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($users) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Full Name</th><th>Role</th><th>Profile Pic</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . htmlspecialchars($user['username']) . "</td>";
            echo "<td>" . htmlspecialchars($user['fname'] ?? '-') . "</td>";
            echo "<td>" . htmlspecialchars($user['role'] ?? '-') . "</td>";
            echo "<td>" . htmlspecialchars($user['profile_pic'] ?? '-') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>Tidak ada data user ditemukan.</p>";
    }
    
    // Cek folder upload
    echo "<h3>Cek Folder Upload:</h3>";
    $uploadDir = 'upload/';
    if (is_dir($uploadDir)) {
        if (is_writable($uploadDir)) {
            echo "<p style='color: green;'>✅ Folder upload ada dan dapat ditulis.</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Folder upload ada tapi tidak dapat ditulis. Periksa permission.</p>";
        }
    } else {
        if (mkdir($uploadDir, 0755, true)) {
            echo "<p style='color: green;'>✅ Folder upload berhasil dibuat.</p>";
        } else {
            echo "<p style='color: red;'>❌ Gagal membuat folder upload.</p>";
        }
    }
    
    echo "<h3>Status Akhir:</h3>";
    echo "<p style='color: green; font-weight: bold;'>Database siap untuk fitur update profil!</p>";
    echo "<p><a href='profil.php'>← Kembali ke Halaman Profil</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background: #f8f9fa;
}
table {
    background: white;
    margin: 10px 0;
}
th {
    background: #007bff;
    color: white;
    padding: 8px;
}
td {
    padding: 8px;
}
h2, h3 {
    color: #333;
}
</style>
