<?php
// Debug script untuk admin panel
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Debug Admin Panel</h2>";

// Test session
session_start();
echo "<h3>Session Status:</h3>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Test database connection
echo "<h3>Database Connection:</h3>";
try {
    include_once __DIR__ . '/../db_conn.php';
    echo "✅ Database connection successful<br>";
    
    // Test tables
    $tables = ['admin', 'users', 'pengurus', 'posts', 'categories', 'comments'];
    foreach ($tables as $table) {
        try {
            $stmt = $conn->query("SELECT COUNT(*) as count FROM $table");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "✅ Table '$table': {$result['count']} records<br>";
        } catch (Exception $e) {
            echo "❌ Table '$table': Error - " . $e->getMessage() . "<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
}

// Test data handlers
echo "<h3>Data Handlers:</h3>";
$handlers = [
    'pengurus' => __DIR__ . '/data/pengurus.php',
    'user' => __DIR__ . '/data/user.php',
    'post' => __DIR__ . '/data/post.php',
    'category' => __DIR__ . '/data/category.php',
    'comment' => __DIR__ . '/data/comment.php'
];

foreach ($handlers as $name => $file) {
    if (file_exists($file)) {
        echo "✅ $name handler exists<br>";
        try {
            include_once $file;
            echo "✅ $name handler loaded successfully<br>";
        } catch (Exception $e) {
            echo "❌ $name handler error: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "❌ $name handler missing<br>";
    }
}

// Test functions
echo "<h3>Function Tests:</h3>";
if (isset($conn)) {
    try {
        if (function_exists('getAllPengurus')) {
            $pengurus = getAllPengurus($conn);
            echo "✅ getAllPengurus(): " . count($pengurus) . " records<br>";
        } else {
            echo "❌ getAllPengurus() function not found<br>";
        }
        
        if (function_exists('getAllUsers')) {
            $users = getAllUsers($conn);
            echo "✅ getAllUsers(): " . count($users) . " records<br>";
        } else {
            echo "❌ getAllUsers() function not found<br>";
        }
        
        if (function_exists('getAllPosts')) {
            $posts = getAllPosts($conn);
            echo "✅ getAllPosts(): " . count($posts) . " records<br>";
        } else {
            echo "❌ getAllPosts() function not found<br>";
        }
        
        if (function_exists('getAllCategories')) {
            $categories = getAllCategories($conn);
            echo "✅ getAllCategories(): " . count($categories) . " records<br>";
        } else {
            echo "❌ getAllCategories() function not found<br>";
        }
        
        if (function_exists('getAllComments')) {
            $comments = getAllComments($conn);
            echo "✅ getAllComments(): " . count($comments) . " records<br>";
        } else {
            echo "❌ getAllComments() function not found<br>";
        }
    } catch (Exception $e) {
        echo "❌ Function test error: " . $e->getMessage() . "<br>";
    }
}

// Test CSS files
echo "<h3>CSS Files:</h3>";
$cssFiles = [
    '../css/bootstrap.min.css',
    '../css/font-awesome.min.css',
    '../css/style.css',
    'css/admin-custom.css'
];

foreach ($cssFiles as $css) {
    if (file_exists($css)) {
        echo "✅ $css exists<br>";
    } else {
        echo "❌ $css missing<br>";
    }
}

// Test admin authentication
echo "<h3>Admin Authentication:</h3>";
if (isset($_SESSION['username'])) {
    echo "✅ User logged in: " . htmlspecialchars($_SESSION['username']) . "<br>";
    
    if (isset($conn)) {
        // Check admin table
        $stmt = $conn->prepare("SELECT * FROM admin WHERE username = ? LIMIT 1");
        $stmt->execute([$_SESSION['username']]);
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($admin) {
            echo "✅ Found in admin table<br>";
        } else {
            echo "⚠️ Not found in admin table, checking users table...<br>";
            
            // Check users table
            $stmt = $conn->prepare("SELECT * FROM users WHERE username = ? LIMIT 1");
            $stmt->execute([$_SESSION['username']]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user) {
                echo "✅ Found in users table - Role: " . ($user['role'] ?? 'No role') . "<br>";
            } else {
                echo "❌ User not found in any table<br>";
            }
        }
    }
} else {
    echo "❌ No user logged in<br>";
}

echo "<h3>File Permissions:</h3>";
$checkDirs = [
    __DIR__,
    __DIR__ . '/css',
    __DIR__ . '/data',
    __DIR__ . '/inc'
];

foreach ($checkDirs as $dir) {
    if (is_readable($dir)) {
        echo "✅ $dir is readable<br>";
    } else {
        echo "❌ $dir is not readable<br>";
    }
}

echo "<h3>PHP Info:</h3>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Error Reporting: " . error_reporting() . "<br>";
echo "Display Errors: " . ini_get('display_errors') . "<br>";

echo "<hr>";
echo "<a href='index.php'>← Back to Admin Panel</a>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
pre { background: #f8f9fa; padding: 10px; border-radius: 5px; }
</style>
