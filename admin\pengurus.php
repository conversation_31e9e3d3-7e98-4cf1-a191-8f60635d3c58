<?php
// Include header (sudah ada session check dan DB connection)
include __DIR__ . '/inc/header.php';

// Include data handler
include_once 'data/pengurus.php';

// Ambil data pengurus dari database
try {
    $pengurus_list = getAllPengurus($conn);
} catch (Exception $e) {
    $pengurus_list = [];
    $error_message = "Error mengambil data pengurus: " . $e->getMessage();
}
?>

<div class="dashboard-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="welcome-header">
            <i class="fa fa-user-tie mr-3"></i>Manajemen Pengurus
        </h1>
        <a href="pengurus-add.php" class="btn btn-brand-primary">
            <i class="fa fa-plus mr-2"></i>Tambah Pengurus
        </a>
    </div>

    <?php if (isset($error_message)): ?>
    <div class="alert alert-danger">
        <i class="fa fa-exclamation-circle mr-2"></i><?= htmlspecialchars($error_message) ?>
    </div>
    <?php endif; ?>

    <?php if (isset($_GET['success'])): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fa fa-check-circle mr-2"></i><?= htmlspecialchars($_GET['success']) ?>
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <?php if (isset($_GET['error'])): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fa fa-exclamation-circle mr-2"></i><?= htmlspecialchars($_GET['error']) ?>
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <div class="card summary-card">
        <div class="card-header card-header-brand-primary">
            <i class="fa fa-user-tie mr-2"></i>Daftar Pengurus (<?= count($pengurus_list) ?> pengurus)
        </div>
        <div class="card-body p-0">
            <?php if (count($pengurus_list) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead style="background: rgba(0,90,153,0.1);">
                        <tr>
                            <th><i class="fa fa-hashtag mr-1"></i>No</th>
                            <th><i class="fa fa-image mr-1"></i>Foto</th>
                            <th><i class="fa fa-user mr-1"></i>Nama</th>
                            <th><i class="fa fa-briefcase mr-1"></i>Jabatan</th>
                            <th><i class="fa fa-instagram mr-1"></i>Instagram</th>
                            <th><i class="fa fa-cogs mr-1"></i>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $no = 1; foreach ($pengurus_list as $pengurus): ?>
                        <tr>
                            <td><?= $no++ ?></td>
                            <td>
                                <?php if (!empty($pengurus['foto'])): ?>
                                    <img src="../upload/<?= htmlspecialchars($pengurus['foto']) ?>"
                                         alt="Foto"
                                         style="width: 50px; height: 50px; border-radius: 50%; object-fit: cover; border: 2px solid #005a99;">
                                <?php else: ?>
                                    <div style="width: 50px; height: 50px; border-radius: 50%; background: linear-gradient(135deg, #005a99, #0066cc); display: flex; align-items: center; justify-content: center; color: #fff; font-weight: bold;">
                                        <?= strtoupper(substr($pengurus['nama'], 0, 1)) ?>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <strong><?= htmlspecialchars($pengurus['nama']) ?></strong>
                            </td>
                            <td>
                                <span class="badge badge-primary px-2 py-1">
                                    <?= htmlspecialchars($pengurus['jabatan'] ?? '-') ?>
                                </span>
                            </td>
                            <td>
                                <?php if (!empty($pengurus['instagram'])): ?>
                                    <a href="<?= htmlspecialchars($pengurus['instagram']) ?>" target="_blank" class="btn btn-sm" style="background: linear-gradient(45deg, #E4405F, #C13584); color: #fff; border-radius: 20px;">
                                        <i class="fa fa-instagram mr-1"></i>Lihat
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="pengurus-edit.php?id=<?= $pengurus['id'] ?>" class="btn btn-sm btn-warning" title="Edit">
                                        <i class="fa fa-edit"></i>
                                    </a>
                                    <a href="pengurus-delete.php?id=<?= $pengurus['id'] ?>" class="btn btn-sm btn-danger"
                                       onclick="return confirm('Apakah Anda yakin ingin menghapus pengurus ini?')" title="Hapus">
                                        <i class="fa fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php else: ?>
            <div class="text-center py-5">
                <i class="fa fa-user-tie" style="font-size: 4rem; color: #ccc;"></i>
                <h4 class="mt-3 text-muted">Belum ada data pengurus</h4>
                <p class="text-muted">Klik tombol "Tambah Pengurus" untuk menambah pengurus baru.</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script src="../js/jquery-1.12.4.min.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script>
$(document).ready(function() {
    // Auto hide alerts
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);

    // Add hover effects
    $('tbody tr').hover(
        function() { $(this).addClass('table-active'); },
        function() { $(this).removeClass('table-active'); }
    );
});
</script>

</body>
</html>
