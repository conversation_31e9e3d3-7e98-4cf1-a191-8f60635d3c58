<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header('Location: ../login.php');
    exit();
}

// Include database connection
include_once __DIR__ . '/../db_conn.php';

$messages = [];
$errors = [];

// Function to execute SQL safely
function executeSQLSafely($conn, $sql, $description) {
    global $messages, $errors;
    try {
        $conn->exec($sql);
        $messages[] = "✅ $description - Berhasil";
        return true;
    } catch (Exception $e) {
        $errors[] = "❌ $description - Error: " . $e->getMessage();
        return false;
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_collation'])) {
    
    // List of tables to fix
    $tables = ['jabatan', 'pengurus', 'users', 'posts', 'post', 'categories', 'category', 'comments', 'comment'];
    
    foreach ($tables as $table) {
        // Check if table exists first
        try {
            $stmt = $conn->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$table]);
            if ($stmt->rowCount() > 0) {
                $sql = "ALTER TABLE `$table` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci";
                executeSQLSafely($conn, $sql, "Fix collation untuk tabel '$table'");
            }
        } catch (Exception $e) {
            // Table doesn't exist, skip
        }
    }
    
    // Recreate jabatan table with correct structure
    $sql = "DROP TABLE IF EXISTS `jabatan`";
    executeSQLSafely($conn, $sql, "Drop existing jabatan table");
    
    $sql = "CREATE TABLE `jabatan` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `nama_jabatan` varchar(50) NOT NULL,
      `deskripsi` text DEFAULT NULL,
      `urutan` int(3) DEFAULT 1,
      `is_active` tinyint(1) DEFAULT 1,
      `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
      `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      UNIQUE KEY `nama_jabatan` (`nama_jabatan`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
    executeSQLSafely($conn, $sql, "Create jabatan table with correct collation");
    
    // Insert default data
    $sql = "INSERT INTO `jabatan` (`nama_jabatan`, `deskripsi`, `urutan`, `is_active`) VALUES
    ('Ketua', 'Ketua UKM Panahan Gendewa Geni', 1, 1),
    ('Wakil Ketua', 'Wakil Ketua UKM Panahan Gendewa Geni', 2, 1),
    ('Sekretaris', 'Sekretaris UKM Panahan Gendewa Geni', 3, 1),
    ('Bendahara', 'Bendahara UKM Panahan Gendewa Geni', 4, 1),
    ('Koordinator Divisi Latihan', 'Koordinator untuk divisi latihan dan pelatihan', 5, 1),
    ('Koordinator Divisi Event', 'Koordinator untuk divisi acara dan event', 6, 1),
    ('Koordinator Divisi Humas', 'Koordinator untuk divisi hubungan masyarakat', 7, 1),
    ('Anggota', 'Anggota UKM Panahan Gendewa Geni', 8, 1)";
    executeSQLSafely($conn, $sql, "Insert default jabatan data");
}

// Include header
include __DIR__ . '/inc/header.php';
?>

<div class="dashboard-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="welcome-header">
            <i class="fa fa-wrench mr-3"></i>Fix Database Collation
        </h1>
        <a href="jabatan.php" class="btn btn-secondary">
            <i class="fa fa-arrow-left mr-2"></i>Kembali ke Jabatan
        </a>
    </div>

    <?php if (!empty($messages)): ?>
    <div class="alert alert-success">
        <i class="fa fa-check-circle mr-2"></i>
        <strong>Perbaikan Berhasil:</strong>
        <ul class="mb-0 mt-2">
            <?php foreach ($messages as $message): ?>
            <li><?= htmlspecialchars($message) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php endif; ?>

    <?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <i class="fa fa-exclamation-circle mr-2"></i>
        <strong>Error yang Terjadi:</strong>
        <ul class="mb-0 mt-2">
            <?php foreach ($errors as $error): ?>
            <li><?= htmlspecialchars($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php endif; ?>

    <div class="card summary-card">
        <div class="card-header card-header-brand-warning">
            <i class="fa fa-exclamation-triangle mr-2"></i>Database Collation Fix
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fa fa-info-circle mr-2"></i>
                <strong>Tentang Perbaikan Ini:</strong>
                <p class="mb-0 mt-2">
                    Tool ini akan memperbaiki masalah collation mismatch yang menyebabkan error 
                    "Illegal mix of collations" dengan cara:
                </p>
                <ul class="mt-2 mb-0">
                    <li>Mengubah semua tabel ke collation utf8mb4_general_ci</li>
                    <li>Membuat ulang tabel jabatan dengan struktur yang benar</li>
                    <li>Menambahkan data default jabatan</li>
                </ul>
            </div>

            <?php if (empty($messages) && empty($errors)): ?>
            <form method="POST">
                <div class="text-center">
                    <h5>Apakah Anda yakin ingin memperbaiki collation database?</h5>
                    <p class="text-muted">Proses ini akan mengubah struktur database dan menambahkan data default jabatan.</p>
                    
                    <button type="submit" name="fix_collation" class="btn btn-brand-warning btn-lg">
                        <i class="fa fa-wrench mr-2"></i>Perbaiki Collation Database
                    </button>
                </div>
            </form>
            <?php else: ?>
            <div class="text-center">
                <h5>Perbaikan Selesai</h5>
                <p class="text-muted">Database telah diperbaiki. Anda dapat kembali ke halaman jabatan.</p>
                
                <a href="jabatan.php" class="btn btn-brand-primary">
                    <i class="fa fa-briefcase mr-2"></i>Ke Halaman Jabatan
                </a>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Database Info -->
    <div class="card summary-card mt-4">
        <div class="card-header card-header-brand-info">
            <i class="fa fa-database mr-2"></i>Informasi Database
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Tabel yang Akan Diperbaiki:</h6>
                    <ul class="list-unstyled">
                        <?php
                        $tables = ['jabatan', 'pengurus', 'users', 'posts', 'post', 'categories', 'category', 'comments', 'comment'];
                        foreach ($tables as $table) {
                            try {
                                $stmt = $conn->prepare("SHOW TABLES LIKE ?");
                                $stmt->execute([$table]);
                                if ($stmt->rowCount() > 0) {
                                    echo '<li><i class="fa fa-check text-success mr-2"></i>' . $table . '</li>';
                                } else {
                                    echo '<li><i class="fa fa-times text-muted mr-2"></i>' . $table . ' (tidak ada)</li>';
                                }
                            } catch (Exception $e) {
                                echo '<li><i class="fa fa-question text-warning mr-2"></i>' . $table . ' (error)</li>';
                            }
                        }
                        ?>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Target Collation:</h6>
                    <p><code>utf8mb4_general_ci</code></p>
                    
                    <h6>Character Set:</h6>
                    <p><code>utf8mb4</code></p>
                    
                    <h6>Engine:</h6>
                    <p><code>InnoDB</code></p>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="../js/jquery-1.12.4.min.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script>
$(document).ready(function() {
    // Auto hide alerts after 10 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 10000);
});
</script>

</body>
</html>
