<?php
include_once __DIR__ . '/../../db_conn.php';

function getAllPengurus($conn) {
    $stmt = $conn->query("SELECT * FROM pengurus ORDER BY id DESC");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getPengurusById($conn, $id) {
    $stmt = $conn->prepare("SELECT * FROM pengurus WHERE id = ? LIMIT 1");
    $stmt->execute([$id]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function addPengurus($conn, $nama, $jabatan, $foto, $instagram) {
    $stmt = $conn->prepare("INSERT INTO pengurus (nama, jabatan, foto, instagram) VALUES (?, ?, ?, ?)");
    return $stmt->execute([$nama, $jabatan, $foto, $instagram]);
}

function updatePengurus($conn, $id, $nama, $jabatan, $foto = null) {
    if ($foto) {
        $stmt = $conn->prepare("UPDATE pengurus SET nama=?, jabatan=?, foto=? WHERE id=?");
        return $stmt->execute([$nama, $jabatan, $foto, $id]);
    } else {
        $stmt = $conn->prepare("UPDATE pengurus SET nama=?, jabatan=? WHERE id=?");
        return $stmt->execute([$nama, $jabatan, $id]);
    }
}

function deletePengurus($conn, $id) {
    $stmt = $conn->prepare("DELETE FROM pengurus WHERE id=?");
    return $stmt->execute([$id]);
}
