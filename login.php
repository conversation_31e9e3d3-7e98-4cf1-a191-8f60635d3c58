<!DOCTYPE html>
<html lang="id">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>Login - UKM Panahan Gendewa Geni</title>
	<meta name="description" content="Login ke website resmi UKM Panahan Universitas Semarang Gendewa Geni">
	
	<!-- Bootstrap CSS -->
	<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3" crossorigin="anonymous">
	
	<!-- FontAwesome -->
	<link rel="stylesheet" href="css/font-awesome.min.css">
	
	<!-- Custom Login CSS -->
	<link rel="stylesheet" href="css/login-custom.css">
	
	<!-- Google Fonts -->
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Teko:wght@300..700&display=swap" rel="stylesheet">
</head>
<body class="login-page">
	<?php include 'inc/navbar.php'; ?>

    <div class="login-container">
    	<div class="login-card">
    		<!-- Login Header -->
    		<div class="login-header">
    			<h1><i class="fa fa-sign-in mr-2"></i>LOGIN</h1>
    			<p>Masuk ke akun UKM Panahan Gendewa Geni</p>
    		</div>

    		<!-- Alert Messages -->
    		<?php if(isset($_GET['error'])): ?>
    		<div class="alert alert-danger" role="alert">
    			<i class="fa fa-exclamation-circle mr-2"></i>
			  	<?php echo htmlspecialchars($_GET['error']); ?>
			</div>
		    <?php endif; ?>

		    <?php if(isset($_GET['success'])): ?>
    		<div class="alert alert-success" role="alert">
    			<i class="fa fa-check-circle mr-2"></i>
			  	<?php echo htmlspecialchars($_GET['success']); ?>
			</div>
		    <?php endif; ?>

    		<!-- Login Form -->
    		<form action="php/login.php" method="post" id="loginForm">
			  	<div class="form-group">
			    	<label class="form-label">
			    		<i class="fa fa-user" style="color: #005a99;"></i>
			    		Username
			    	</label>
			    	<input type="text" 
			           class="form-control modern-input"
			           name="uname"
			           id="username"
			           placeholder="Masukkan username Anda"
			           value="<?php echo isset($_GET['uname']) ? htmlspecialchars($_GET['uname']) : '' ?>"
			           required>
			  	</div>
<br>
			  	<div class="form-group">
			    	<label class="form-label">
			    		<i class="fa fa-lock" style="color: #ff9800;"></i>
			    		Password
			    	</label>
			    	<div style="position: relative;">
				    	<input type="password" 
				           class="form-control modern-input"
				           name="pass"
				           id="password"
				           placeholder="Masukkan password Anda"
				           required>
				        <span class="password-toggle" onclick="togglePassword()">
				        	<i class="fa fa-eye" id="toggleIcon"></i>
				        </span>
			        </div>
			  	</div>
<br>
			  	<button type="submit" class="btn-login" id="loginBtn">
			  		<i class="fa fa-sign-in mr-2"></i>
			  		Masuk
			  	</button>

			  	<div class="signup-link">
			  		<p>Belum punya akun? <a href="signup.php">Daftar di sini</a></p>
			  	</div>
			</form>
    	</div>
    </div>
		
    <?php include 'inc/footer.php'; ?>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p" crossorigin="anonymous"></script>

    <script>
    // Toggle password visibility
    function togglePassword() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.getElementById('toggleIcon');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }

    // Form validation and loading state
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        const loginBtn = document.getElementById('loginBtn');
        
        // Basic validation
        if (username.length < 3) {
            alert('Username minimal 3 karakter!');
            e.preventDefault();
            return;
        }
        
        if (password.length < 1) {
            alert('Password tidak boleh kosong!');
            e.preventDefault();
            return;
        }
        
        // Add loading state
        loginBtn.classList.add('loading');
        loginBtn.innerHTML = '<i class="fa fa-spinner fa-spin mr-2"></i>Memproses...';
        loginBtn.disabled = true;
    });

    // Auto hide alerts after 5 seconds
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            if (alert.classList.contains('fade')) {
                alert.classList.remove('show');
            } else {
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-20px)';
                setTimeout(() => alert.remove(), 300);
            }
        });
    }, 5000);

    // Add focus effects
    document.querySelectorAll('.modern-input').forEach(function(input) {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
    </script>
</body>
</html>
