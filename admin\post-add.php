<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header('Location: ../login.php');
    exit();
}

// Include database connection
include_once __DIR__ . '/../db_conn.php';

// Include data handlers
include_once __DIR__ . '/data/post.php';
include_once __DIR__ . '/data/category.php';

$errors = [];
$success = false;

// Get categories for dropdown
try {
    $categories = function_exists('getAllCategories') ? getAllCategories($conn) : [];
} catch (Exception $e) {
    $categories = [];
    $errors[] = 'Error mengambil data kategori: ' . $e->getMessage();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = trim($_POST['title'] ?? '');
    $content = trim($_POST['content'] ?? '');
    $category_id = intval($_POST['category_id'] ?? 0);
    $status = $_POST['status'] ?? 'published';

    // Validation
    if (empty($title)) {
        $errors[] = 'Judul tidak boleh kosong';
    } elseif (strlen($title) < 5) {
        $errors[] = 'Judul minimal 5 karakter';
    }

    if (empty($content)) {
        $errors[] = 'Konten tidak boleh kosong';
    } elseif (strlen($content) < 50) {
        $errors[] = 'Konten minimal 50 karakter';
    }

    if ($category_id <= 0) {
        $errors[] = 'Pilih kategori yang valid';
    }

    // Handle cover upload
    $cover_url = '';
    if (isset($_FILES['cover']) && $_FILES['cover']['error'] === UPLOAD_ERR_OK) {
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $maxSize = 10 * 1024 * 1024; // 10MB

        if (!in_array($_FILES['cover']['type'], $allowedTypes)) {
            $errors[] = 'Tipe file cover tidak diizinkan. Gunakan JPG, PNG, GIF, atau WebP';
        } elseif ($_FILES['cover']['size'] > $maxSize) {
            $errors[] = 'Ukuran file cover terlalu besar. Maksimal 10MB';
        } else {
            $uploadDir = __DIR__ . '/../upload/blog/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            $fileExtension = pathinfo($_FILES['cover']['name'], PATHINFO_EXTENSION);
            $cover_url = 'post_' . time() . '_' . uniqid() . '.' . $fileExtension;

            if (!move_uploaded_file($_FILES['cover']['tmp_name'], $uploadDir . $cover_url)) {
                $errors[] = 'Gagal mengupload cover';
                $cover_url = '';
            }
        }
    }

    // Insert to database
    if (empty($errors)) {
        try {
            $stmt = $conn->prepare("INSERT INTO post (post_title, post_text, category_id, cover_url) VALUES (?, ?, ?, ?)");
            $result = $stmt->execute([$title, $content, $category_id, $cover_url]);

            if ($result) {
                header('Location: post.php?success=' . urlencode('Postingan berhasil ditambahkan'));
                exit();
            } else {
                $errors[] = 'Gagal menyimpan postingan ke database';
            }
        } catch (Exception $e) {
            $errors[] = 'Error: ' . $e->getMessage();
        }
    }
}

// Include header setelah semua redirect logic
include __DIR__ . '/inc/header.php';
?>

<div class="dashboard-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="welcome-header">
            <i class="fa fa-plus mr-3"></i>Tambah Postingan Baru
        </h1>
        <a href="post.php" class="btn btn-secondary">
            <i class="fa fa-arrow-left mr-2"></i>Kembali
        </a>
    </div>

    <?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <i class="fa fa-exclamation-circle mr-2"></i>
        <ul class="mb-0">
            <?php foreach ($errors as $error): ?>
            <li><?= htmlspecialchars($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php endif; ?>

    <div class="card summary-card">
        <div class="card-header card-header-brand-warning">
            <i class="fa fa-plus mr-2"></i>Form Tambah Postingan
        </div>
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group">
                            <label for="title"><i class="fa fa-file-text mr-1"></i>Judul Postingan *</label>
                            <input type="text" class="form-control" id="title" name="title"
                                   value="<?= htmlspecialchars($_POST['title'] ?? '') ?>"
                                   placeholder="Masukkan judul postingan yang menarik" required>
                            <small class="form-text text-muted">Minimal 5 karakter, maksimal 200 karakter</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="category_id"><i class="fa fa-tag mr-1"></i>Kategori *</label>
                            <select class="form-control" id="category_id" name="category_id" required>
                                <option value="">Pilih Kategori</option>
                                <?php foreach ($categories as $cat): ?>
                                <option value="<?= $cat['id'] ?>" <?= ($_POST['category_id'] ?? '') == $cat['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($cat['category']) ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="content"><i class="fa fa-edit mr-1"></i>Konten Postingan *</label>
                    <textarea class="form-control" id="content" name="content" rows="15"
                              placeholder="Tulis konten postingan di sini..." required><?= htmlspecialchars($_POST['content'] ?? '') ?></textarea>
                    <small class="form-text text-muted">Minimal 50 karakter. Gunakan HTML untuk formatting</small>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="tags"><i class="fa fa-tags mr-1"></i>Tags</label>
                            <input type="text" class="form-control" id="tags" name="tags"
                                   value="<?= htmlspecialchars($_POST['tags'] ?? '') ?>"
                                   placeholder="tag1, tag2, tag3">
                            <small class="form-text text-muted">Pisahkan dengan koma untuk multiple tags</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="status"><i class="fa fa-eye mr-1"></i>Status Publikasi</label>
                            <select class="form-control" id="status" name="status">
                                <option value="published" <?= ($_POST['status'] ?? 'published') == 'published' ? 'selected' : '' ?>>
                                    <i class="fa fa-check"></i> Published
                                </option>
                                <option value="draft" <?= ($_POST['status'] ?? '') == 'draft' ? 'selected' : '' ?>>
                                    <i class="fa fa-edit"></i> Draft
                                </option>
                                <option value="private" <?= ($_POST['status'] ?? '') == 'private' ? 'selected' : '' ?>>
                                    <i class="fa fa-lock"></i> Private
                                </option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="cover"><i class="fa fa-image mr-1"></i>Cover Image</label>
                    <div class="custom-file">
                        <input type="file" class="custom-file-input" id="cover" name="cover" accept="image/*">
                        <label class="custom-file-label" for="cover">Pilih cover image...</label>
                    </div>
                    <small class="form-text text-muted">
                        Format: JPG, PNG, GIF, WebP. Maksimal 10MB. Disarankan ukuran 1200x630px untuk optimal sharing.
                    </small>
                    <div id="imagePreview" class="mt-3" style="display: none;">
                        <img id="preview" src="" alt="Preview" style="max-width: 300px; max-height: 200px; border-radius: 10px; border: 2px solid #ff9800;">
                    </div>
                </div>

                <div class="form-group text-right">
                    <a href="post.php" class="btn btn-secondary mr-2">
                        <i class="fa fa-times mr-1"></i>Batal
                    </a>
                    <button type="submit" class="btn btn-brand-warning">
                        <i class="fa fa-save mr-1"></i>Simpan Postingan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="../js/jquery-1.12.4.min.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script>
$(document).ready(function() {
    // Custom file input
    $('.custom-file-input').on('change', function() {
        var fileName = $(this).val().split('\\').pop();
        $(this).next('.custom-file-label').html(fileName);

        // Image preview
        if (this.files && this.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                $('#preview').attr('src', e.target.result);
                $('#imagePreview').show();
            }
            reader.readAsDataURL(this.files[0]);
        }
    });

    // Auto-resize textarea
    $('textarea').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });

    // Character counter for title
    $('#title').on('input', function() {
        var length = $(this).val().length;
        var maxLength = 200;
        var remaining = maxLength - length;

        if (!$(this).next('.char-counter').length) {
            $(this).after('<small class="char-counter text-muted"></small>');
        }

        $(this).next('.char-counter').text(remaining + ' karakter tersisa');

        if (remaining < 0) {
            $(this).addClass('is-invalid');
            $(this).next('.char-counter').removeClass('text-muted').addClass('text-danger');
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.char-counter').removeClass('text-danger').addClass('text-muted');
        }
    });

    // Form validation
    $('form').on('submit', function(e) {
        var isValid = true;

        // Check required fields
        $(this).find('input[required], select[required], textarea[required]').each(function() {
            if (!$(this).val().trim()) {
                $(this).addClass('is-invalid');
                isValid = false;
            } else {
                $(this).removeClass('is-invalid');
            }
        });

        // Check title length
        var title = $('#title').val();
        if (title.length < 5 || title.length > 200) {
            $('#title').addClass('is-invalid');
            isValid = false;
        }

        // Check content length
        var content = $('#content').val();
        if (content.length < 50) {
            $('#content').addClass('is-invalid');
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
            alert('Mohon periksa kembali form Anda!');
        }
    });
});
</script>

</body>
</html>
