<?php
// Include header (sudah ada session check dan DB connection)
include __DIR__ . '/inc/header.php';

// Real-time data fetch dengan error handling
try {
    $pengurus_count = 0;
    $users_count = 0;
    $posts_count = 0;
    $categories_count = 0;
    $comments_count = 0;

    // Count pengurus
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM pengurus");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $pengurus_count = $result['count'] ?? 0;
    } catch (Exception $e) {
        $pengurus_count = 0;
        error_log("Error counting pengurus: " . $e->getMessage());
    }

    // Count users
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM users");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $users_count = $result['count'] ?? 0;
    } catch (Exception $e) {
        $users_count = 0;
        error_log("Error counting users: " . $e->getMessage());
    }

    // Count posts - prioritas tabel 'post' terlebih dahulu
    try {
        // Coba dengan nama tabel 'post' dulu (sesuai database user)
        $stmt = $conn->query("SELECT COUNT(*) as count FROM post");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $posts_count = $result['count'] ?? 0;
    } catch (Exception $e) {
        try {
            // Fallback ke nama tabel 'posts' jika 'post' tidak ada
            $stmt = $conn->query("SELECT COUNT(*) as count FROM posts");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $posts_count = $result['count'] ?? 0;
        } catch (Exception $e2) {
            $posts_count = 0;
            error_log("Error counting posts: " . $e2->getMessage());
        }
    }

    // Count categories - prioritas tabel 'category' terlebih dahulu
    try {
        // Coba dengan nama tabel 'category' dulu (sesuai database user)
        $stmt = $conn->query("SELECT COUNT(*) as count FROM category");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $categories_count = $result['count'] ?? 0;
    } catch (Exception $e) {
        try {
            // Fallback ke nama tabel 'categories' jika 'category' tidak ada
            $stmt = $conn->query("SELECT COUNT(*) as count FROM categories");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $categories_count = $result['count'] ?? 0;
        } catch (Exception $e2) {
            $categories_count = 0;
            error_log("Error counting categories: " . $e2->getMessage());
        }
    }

    // Count comments
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM comments");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $comments_count = $result['count'] ?? 0;
    } catch (Exception $e) {
        $comments_count = 0;
        error_log("Error counting comments: " . $e->getMessage());
    }

} catch (Exception $e) {
    error_log("Database error in admin dashboard: " . $e->getMessage());
    // Set default values jika ada error
    $pengurus_count = $users_count = $posts_count = $categories_count = $comments_count = 0;
}

// Get recent data for dashboard tables
try {
    // Recent posts
    $recent_posts = [];
    try {
        $stmt = $conn->query("SELECT id, post_title, created_at FROM post ORDER BY created_at DESC LIMIT 5");
        $recent_posts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        try {
            $stmt = $conn->query("SELECT id, post_title, created_at FROM posts ORDER BY created_at DESC LIMIT 5");
            $recent_posts = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e2) {
            $recent_posts = [];
        }
    }

    // Recent users
    $recent_users = [];
    try {
        $stmt = $conn->query("SELECT id, username, email, created_at FROM users ORDER BY created_at DESC LIMIT 5");
        $recent_users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        $recent_users = [];
    }

    // Recent pengurus
    $recent_pengurus = [];
    try {
        $stmt = $conn->query("SELECT id, nama, jabatan, created_at FROM pengurus ORDER BY created_at DESC LIMIT 5");
        $recent_pengurus = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        $recent_pengurus = [];
    }

    // Recent categories
    $recent_categories = [];
    try {
        $stmt = $conn->query("SELECT id, category, created_at FROM category ORDER BY created_at DESC LIMIT 5");
        $recent_categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        try {
            $stmt = $conn->query("SELECT id, category, created_at FROM categories ORDER BY created_at DESC LIMIT 5");
            $recent_categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e2) {
            $recent_categories = [];
        }
    }

} catch (Exception $e) {
    error_log("Error fetching recent data: " . $e->getMessage());
    $recent_posts = $recent_users = $recent_pengurus = $recent_categories = [];
}
?>

<!-- Dashboard Content -->
<div class="dashboard-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="welcome-header">
            <i class="fa fa-dashboard mr-3"></i>Dashboard Admin - Selamat Datang <?= htmlspecialchars($_SESSION['username']) ?>
        </h1>
        <div class="btn-group">
            <button class="btn btn-outline-primary btn-sm" onclick="refreshStats()">
                <i class="fa fa-refresh mr-1"></i>Refresh Data
            </button>
            <a href="../index.php" class="btn btn-outline-secondary btn-sm" target="_blank">
                <i class="fa fa-external-link mr-1"></i>Lihat Website
            </a>
        </div>
    </div>
    
    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-2 col-6 mb-3">
            <div class="card admin-card text-center p-3">
                <div class="admin-icon icon-pengurus">
                    <i class="fa fa-users"></i>
                </div>
                <div class="summary-title">Pengurus</div>
                <div class="mb-3">Jumlah: <b id="pengurus-count"><?= $pengurus_count ?></b></div>
                <a href="pengurus.php" class="btn btn-brand-primary btn-sm">
                    <i class="fa fa-cogs mr-1"></i>Kelola
                </a>
            </div>
        </div>

        <div class="col-md-2 col-6 mb-3">
            <div class="card admin-card text-center p-3">
                <div class="admin-icon icon-users">
                    <i class="fa fa-user"></i>
                </div>
                <div class="summary-title">Pengguna</div>
                <div class="mb-3">Jumlah: <b id="users-count"><?= $users_count ?></b></div>
                <a href="users.php" class="btn btn-brand-success btn-sm">
                    <i class="fa fa-user-plus mr-1"></i>Kelola
                </a>
            </div>
        </div>

        <div class="col-md-2 col-6 mb-3">
            <div class="card admin-card text-center p-3">
                <div class="admin-icon icon-posts">
                    <i class="fa fa-newspaper-o"></i>
                </div>
                <div class="summary-title">Postingan</div>
                <div class="mb-3">Jumlah: <b id="posts-count"><?= $posts_count ?></b></div>
                <a href="post.php" class="btn btn-brand-warning btn-sm">
                    <i class="fa fa-edit mr-1"></i>Kelola
                </a>
            </div>
        </div>

        <div class="col-md-2 col-6 mb-3">
            <div class="card admin-card text-center p-3">
                <div class="admin-icon icon-categories">
                    <i class="fa fa-tags"></i>
                </div>
                <div class="summary-title">Kategori</div>
                <div class="mb-3">Jumlah: <b id="categories-count"><?= $categories_count ?></b></div>
                <a href="category.php" class="btn btn-brand-info btn-sm">
                    <i class="fa fa-folder-open mr-1"></i>Kelola
                </a>
            </div>
        </div>

      
        <div class="col-md-2 col-6 mb-3">
            <div class="card admin-card text-center p-3">
                <div class="admin-icon icon-logout">
                    <i class="fa fa-home"></i>
                </div>
                <div class="summary-title">Website</div>
                <div class="mb-3">Kembali</div>
                <a href="../index.php" class="btn btn-brand-danger btn-sm">
                    <i class="fa fa-external-link mr-1"></i>Lihat
                </a>
            </div>
        </div>
    </div>

    <!-- Dashboard Tables -->
   

    <!-- Quick Info -->
    <div class="row">
        <div class="col-12">
            <div class="card summary-card">
                <div class="card-header card-header-brand-primary">
                    <i class="fa fa-info-circle mr-2"></i>Informasi Sistem
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong><i class="fa fa-user mr-2" style="color: #005a99;"></i>User Login:</strong> <?= htmlspecialchars($_SESSION['username']) ?></p>
                            <p><strong><i class="fa fa-clock-o mr-2" style="color: #ff9800;"></i>Waktu Akses:</strong> <span id="current-time"><?= date('d/m/Y H:i:s') ?></span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong><i class="fa fa-database mr-2" style="color: #17a2b8;"></i>Total Data:</strong> <span id="total-data"><?= $pengurus_count + $users_count + $posts_count + $categories_count + $comments_count ?></span></p>
                            <p><strong><i class="fa fa-signal mr-2" style="color: #28a745;"></i>Status:</strong> <span class="text-success">Online & Aktif</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="../js/jquery-1.12.4.min.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script>
$(document).ready(function() {
    // Ensure all admin cards are always visible
    $('.admin-card').css({
        'opacity': '1',
        'visibility': 'visible',
        'display': 'block'
    });

    // Add simple hover effects
    $('.admin-card').hover(
        function() {
            $(this).addClass('shadow-lg');
        },
        function() {
            $(this).removeClass('shadow-lg');
        }
    );

    // Real-time clock
    function updateClock() {
        var now = new Date();
        var timeString = now.toLocaleTimeString('id-ID');
        var dateString = now.toLocaleDateString('id-ID', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        $('#current-time').text(dateString + ' - ' + timeString);
    }

    updateClock();
    setInterval(updateClock, 1000);

    // Auto refresh data every 30 seconds
    setInterval(refreshStats, 30000);
});

// Function to refresh statistics
function refreshStats() {
    $.ajax({
        url: 'ajax/get-stats.php',
        type: 'GET',
        dataType: 'json',
        success: function(data) {
            if (data.success) {
                $('#pengurus-count').text(data.pengurus_count);
                $('#users-count').text(data.users_count);
                $('#posts-count').text(data.posts_count);
                $('#categories-count').text(data.categories_count);
                $('#comments-count').text(data.comments_count);
                $('#total-data').text(data.total_count);

                // Add animation effect
                $('.admin-card').addClass('pulse');
                setTimeout(function() {
                    $('.admin-card').removeClass('pulse');
                }, 1000);
            }
        },
        error: function() {
            console.log('Error refreshing stats');
        }
    });
}
</script>

<style>
.pulse {
    animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.navbar-nav .nav-link:hover {
    background: rgba(255,255,255,0.2);
    border-radius: 8px;
    transform: translateY(-1px);
}

.dropdown-menu {
    border: none;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-radius: 10px;
}

.dropdown-item:hover {
    background: rgba(0,90,153,0.1);
    color: #005a99;
}

#clock {
    animation: fadeIn 1s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
</style>

</body>
</html>
