<?php
// Include header (sudah ada session check dan DB connection)
include __DIR__ . '/inc/header.php';

// Ambil data pengguna dari database
try {
    $stmt = $conn->query("SELECT id, username, fname, role, profile_pic, created_at FROM users ORDER BY created_at DESC");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $users = [];
    $error_message = "Error mengambil data pengguna: " . $e->getMessage();
}
?>

<div class="dashboard-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="welcome-header">
            <i class="fa fa-users mr-3"></i>Manajemen Pengguna
        </h1>
        <a href="user-add.php" class="btn btn-brand-success">
            <i class="fa fa-user-plus mr-2"></i>Tambah Pengguna
        </a>
    </div>

    <?php if (isset($error_message)): ?>
    <div class="alert alert-danger">
        <i class="fa fa-exclamation-circle mr-2"></i><?= htmlspecialchars($error_message) ?>
    </div>
    <?php endif; ?>

    <?php if (isset($_GET['success'])): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fa fa-check-circle mr-2"></i><?= htmlspecialchars($_GET['success']) ?>
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <?php if (isset($_GET['error'])): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fa fa-exclamation-circle mr-2"></i><?= htmlspecialchars($_GET['error']) ?>
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <div class="card summary-card">
        <div class="card-header card-header-brand-success">
            <i class="fa fa-users mr-2"></i>Daftar Pengguna (<?= count($users) ?> pengguna)
        </div>
        <div class="card-body p-0">
            <?php if (count($users) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead style="background: rgba(40,167,69,0.1);">
                        <tr>
                            <th><i class="fa fa-hashtag mr-1"></i>ID</th>
                            <th><i class="fa fa-user mr-1"></i>Foto</th>
                            <th><i class="fa fa-user mr-1"></i>Username</th>
                            <th><i class="fa fa-id-card mr-1"></i>Nama Lengkap</th>
                            <th><i class="fa fa-shield mr-1"></i>Role</th>
                            <th><i class="fa fa-calendar mr-1"></i>Terdaftar</th>
                            <th><i class="fa fa-cogs mr-1"></i>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($users as $user): ?>
                        <tr>
                            <td><?= htmlspecialchars($user['id']) ?></td>
                            <td>
                                <?php if ($user['profile_pic']): ?>
                                    <img src="../upload/<?= htmlspecialchars($user['profile_pic']) ?>"
                                         alt="Profile"
                                         style="width: 40px; height: 40px; border-radius: 50%; object-fit: cover; border: 2px solid #28a745;">
                                <?php else: ?>
                                    <div style="width: 40px; height: 40px; border-radius: 50%; background: linear-gradient(135deg, #28a745, #20c997); display: flex; align-items: center; justify-content: center; color: #fff; font-weight: bold;">
                                        <?= strtoupper(substr($user['username'], 0, 1)) ?>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <strong><?= htmlspecialchars($user['username']) ?></strong>
                            </td>
                            <td><?= htmlspecialchars($user['fname'] ?? '-') ?></td>
                            <td>
                                <span class="badge <?= $user['role'] == 'admin' ? 'badge-danger' : 'badge-primary' ?> px-2 py-1">
                                    <?= htmlspecialchars($user['role'] ?? 'Anggota') ?>
                                </span>
                            </td>
                            <td><?= $user['created_at'] ? date('d/m/Y', strtotime($user['created_at'])) : '-' ?></td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="user-edit.php?id=<?= $user['id'] ?>" class="btn btn-sm btn-warning" title="Edit">
                                        <i class="fa fa-edit"></i>
                                    </a>
                                    <a href="user-delete.php?id=<?= $user['id'] ?>" class="btn btn-sm btn-danger"
                                       onclick="return confirm('Apakah Anda yakin ingin menghapus pengguna ini?')" title="Hapus">
                                        <i class="fa fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php else: ?>
            <div class="text-center py-5">
                <i class="fa fa-users" style="font-size: 4rem; color: #ccc;"></i>
                <h4 class="mt-3 text-muted">Belum ada pengguna</h4>
                <p class="text-muted">Klik tombol "Tambah Pengguna" untuk menambah pengguna baru.</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script src="../js/jquery-1.12.4.min.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script>
$(document).ready(function() {
    // Auto hide alerts
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);

    // Add hover effects
    $('tbody tr').hover(
        function() { $(this).addClass('table-active'); },
        function() { $(this).removeClass('table-active'); }
    );
});
</script>

</body>
</html>
