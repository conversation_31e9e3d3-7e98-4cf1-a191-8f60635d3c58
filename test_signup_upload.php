<?php
// Script untuk test fitur upload foto profil pada signup
include_once 'db_conn.php';

echo "<h2>Test Fitur Upload Foto Profil - Signup</h2>";

try {
    // Cek struktur tabel users
    $stmt = $conn->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Struktur Tabel Users:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    $hasProfilePic = false;
    $hasFname = false;
    $hasRole = false;
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'profile_pic') $hasProfilePic = true;
        if ($column['Field'] === 'fname') $hasFname = true;
        if ($column['Field'] === 'role') $hasRole = true;
    }
    echo "</table>";
    
    // Status kolom yang diperlukan
    echo "<h3>Status Kolom yang Diperlukan:</h3>";
    echo "<ul>";
    echo "<li>profile_pic: " . ($hasProfilePic ? "✅ Ada" : "❌ Tidak ada") . "</li>";
    echo "<li>fname: " . ($hasFname ? "✅ Ada" : "❌ Tidak ada") . "</li>";
    echo "<li>role: " . ($hasRole ? "✅ Ada" : "❌ Tidak ada") . "</li>";
    echo "</ul>";
    
    // Cek folder upload
    echo "<h3>Status Folder Upload:</h3>";
    $uploadDir = 'upload/';
    if (is_dir($uploadDir)) {
        if (is_writable($uploadDir)) {
            echo "<p style='color: green;'>✅ Folder upload ada dan dapat ditulis.</p>";
            echo "<p>Path: " . realpath($uploadDir) . "</p>";
            echo "<p>Permissions: " . substr(sprintf('%o', fileperms($uploadDir)), -4) . "</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Folder upload ada tapi tidak dapat ditulis.</p>";
            echo "<p>Jalankan: <code>chmod 755 upload/</code></p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Folder upload tidak ada.</p>";
        if (mkdir($uploadDir, 0755, true)) {
            echo "<p style='color: green;'>✅ Folder upload berhasil dibuat.</p>";
        } else {
            echo "<p style='color: red;'>❌ Gagal membuat folder upload.</p>";
        }
    }
    
    // Cek konfigurasi PHP untuk upload
    echo "<h3>Konfigurasi PHP Upload:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Setting</th><th>Value</th><th>Status</th></tr>";
    
    $uploadMaxFilesize = ini_get('upload_max_filesize');
    $postMaxSize = ini_get('post_max_size');
    $maxFileUploads = ini_get('max_file_uploads');
    $fileUploads = ini_get('file_uploads');
    
    echo "<tr><td>file_uploads</td><td>$fileUploads</td><td>" . ($fileUploads ? "✅" : "❌") . "</td></tr>";
    echo "<tr><td>upload_max_filesize</td><td>$uploadMaxFilesize</td><td>" . (intval($uploadMaxFilesize) >= 2 ? "✅" : "⚠️") . "</td></tr>";
    echo "<tr><td>post_max_size</td><td>$postMaxSize</td><td>" . (intval($postMaxSize) >= 2 ? "✅" : "⚠️") . "</td></tr>";
    echo "<tr><td>max_file_uploads</td><td>$maxFileUploads</td><td>" . ($maxFileUploads >= 1 ? "✅" : "❌") . "</td></tr>";
    echo "</table>";
    
    // Test data sample
    echo "<h3>Sample Data Users (5 terbaru):</h3>";
    $stmt = $conn->query("SELECT id, username, fname, role, profile_pic, created_at FROM users ORDER BY id DESC LIMIT 5");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($users) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Nama</th><th>Role</th><th>Foto</th><th>Tanggal</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . htmlspecialchars($user['username']) . "</td>";
            echo "<td>" . htmlspecialchars($user['fname'] ?? '-') . "</td>";
            echo "<td>" . htmlspecialchars($user['role'] ?? '-') . "</td>";
            echo "<td>";
            if ($user['profile_pic']) {
                echo "<img src='upload/" . htmlspecialchars($user['profile_pic']) . "' style='width: 40px; height: 40px; border-radius: 50%; object-fit: cover;' alt='Profile'>";
                echo "<br><small>" . htmlspecialchars($user['profile_pic']) . "</small>";
            } else {
                echo "No photo";
            }
            echo "</td>";
            echo "<td>" . ($user['created_at'] ?? '-') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>Belum ada data user.</p>";
    }
    
    // Test form signup
    echo "<h3>Test Form Signup:</h3>";
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h4>Checklist Fitur:</h4>";
    echo "<ul>";
    echo "<li>✅ Form memiliki enctype='multipart/form-data'</li>";
    echo "<li>✅ Field foto profil opsional</li>";
    echo "<li>✅ Preview foto real-time</li>";
    echo "<li>✅ Validasi client-side (JavaScript)</li>";
    echo "<li>✅ Validasi server-side (PHP)</li>";
    echo "<li>✅ Error handling komprehensif</li>";
    echo "<li>✅ Responsive design</li>";
    echo "</ul>";
    echo "</div>";
    
    // Instruksi testing
    echo "<h3>Cara Test Manual:</h3>";
    echo "<ol>";
    echo "<li><a href='signup.php' target='_blank'>Buka halaman signup</a></li>";
    echo "<li>Isi semua field yang required (nama, username, password)</li>";
    echo "<li>Pilih foto profil (opsional) - test dengan berbagai format dan ukuran</li>";
    echo "<li>Lihat preview foto yang muncul</li>";
    echo "<li>Submit form dan cek apakah berhasil</li>";
    echo "<li>Login dengan akun baru dan cek foto profil di halaman profil</li>";
    echo "</ol>";
    
    // Test cases
    echo "<h3>Test Cases yang Harus Dicoba:</h3>";
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h4>Positive Test Cases:</h4>";
    echo "<ul>";
    echo "<li>Signup tanpa foto profil (harus berhasil)</li>";
    echo "<li>Signup dengan foto JPG valid (< 2MB)</li>";
    echo "<li>Signup dengan foto PNG valid (< 2MB)</li>";
    echo "<li>Signup dengan foto GIF valid (< 2MB)</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<h4>Negative Test Cases:</h4>";
    echo "<ul>";
    echo "<li>Upload file > 2MB (harus ditolak)</li>";
    echo "<li>Upload file format tidak didukung (.txt, .pdf, dll)</li>";
    echo "<li>Username yang sudah ada (harus ditolak)</li>";
    echo "<li>Password < 6 karakter (harus ditolak)</li>";
    echo "<li>Field required kosong (harus ditolak)</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h3>Status Akhir:</h3>";
    if ($hasProfilePic && $hasFname && $hasRole && is_writable($uploadDir) && $fileUploads) {
        echo "<p style='color: green; font-weight: bold; font-size: 18px;'>✅ Semua fitur siap untuk testing!</p>";
    } else {
        echo "<p style='color: red; font-weight: bold; font-size: 18px;'>❌ Ada masalah yang perlu diperbaiki!</p>";
    }
    
    echo "<p><a href='signup.php'>← Test Signup Form</a> | <a href='login.php'>← Test Login</a> | <a href='profil.php'>← Test Profil</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>Database Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    margin: 20px;
    background: #f8f9fa;
}
table {
    background: white;
    margin: 10px 0;
}
th {
    background: #007bff;
    color: white;
    padding: 8px;
}
td {
    padding: 8px;
}
h2, h3, h4 {
    color: #333;
}
code {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: monospace;
}
</style>
