<?php

if(isset($_POST['uname']) &&
   isset($_POST['pass']) &&
   isset($_POST['fname'])){

    include "../db_conn.php";

    $uname = trim($_POST['uname']);
    $pass = trim($_POST['pass']);
    $fname = trim($_POST['fname']);
    $profile_pic = null;

    $data = "uname=" . urlencode($uname) . "&fname=" . urlencode($fname);

    // Validasi input
    if(empty($uname)){
    	$em = "Username tidak boleh kosong";
    	header("Location: ../signup.php?error=" . urlencode($em) . "&$data");
	    exit;
    }else if(strlen($uname) < 3){
    	$em = "Username minimal 3 karakter";
    	header("Location: ../signup.php?error=" . urlencode($em) . "&$data");
	    exit;
    }else if(empty($pass)){
    	$em = "Password tidak boleh kosong";
    	header("Location: ../signup.php?error=" . urlencode($em) . "&$data");
	    exit;
    }else if(strlen($pass) < 6){
    	$em = "Password minimal 6 karakter";
    	header("Location: ../signup.php?error=" . urlencode($em) . "&$data");
	    exit;
    }else if(empty($fname)){
    	$em = "Nama lengkap tidak boleh kosong";
    	header("Location: ../signup.php?error=" . urlencode($em) . "&$data");
	    exit;
    }else {

    	// Check if username already exists
    	$sql = "SELECT * FROM users WHERE username = ?";
    	$stmt = $conn->prepare($sql);
    	$stmt->execute([$uname]);
    	if($stmt->rowCount() > 0){
    		$em = "Username sudah digunakan, silakan pilih yang lain";
    		header("Location: ../signup.php?error=" . urlencode($em) . "&$data");
    		exit;
    	}

    	// Handle profile picture upload (opsional)
    	if(isset($_FILES['profile_pic']) && $_FILES['profile_pic']['error'] === UPLOAD_ERR_OK) {
    		$allowed = ['jpg', 'jpeg', 'png', 'gif'];
    		$maxSize = 2 * 1024 * 1024; // 2MB
    		$fileName = $_FILES['profile_pic']['name'];
    		$fileSize = $_FILES['profile_pic']['size'];
    		$fileTmpName = $_FILES['profile_pic']['tmp_name'];
    		$ext = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

    		// Validasi file
    		if(!in_array($ext, $allowed)) {
    			$em = "Format foto harus JPG, PNG, atau GIF";
    			header("Location: ../signup.php?error=" . urlencode($em) . "&$data");
    			exit;
    		} elseif($fileSize > $maxSize) {
    			$em = "Ukuran foto maksimal 2MB";
    			header("Location: ../signup.php?error=" . urlencode($em) . "&$data");
    			exit;
    		} else {
    			// Buat direktori upload jika belum ada
    			$uploadDir = '../upload/';
    			if(!is_dir($uploadDir)) {
    				mkdir($uploadDir, 0755, true);
    			}

    			// Generate nama file unik
    			$profile_pic = 'profile_' . time() . '_' . rand(100,999) . '.' . $ext;
    			$dest = $uploadDir . $profile_pic;

    			if(!move_uploaded_file($fileTmpName, $dest)) {
    				$em = "Gagal mengupload foto profil";
    				header("Location: ../signup.php?error=" . urlencode($em) . "&$data");
    				exit;
    			}
    		}
    	}

    	// Hash password
    	$hashed_pass = password_hash($pass, PASSWORD_DEFAULT);

    	// Insert user ke database
    	$sql = "INSERT INTO users(username, password, fname, profile_pic, role)
    	        VALUES(?, ?, ?, ?, 'Anggota')";
    	$stmt = $conn->prepare($sql);

    	if($stmt->execute([$uname, $hashed_pass, $fname, $profile_pic])) {
    		$success_msg = "Akun berhasil dibuat! Silakan login dengan akun Anda.";
    		header("Location: ../login.php?success=" . urlencode($success_msg));
    		exit;
    	} else {
    		// Hapus foto jika gagal insert ke database
    		if($profile_pic && file_exists($uploadDir . $profile_pic)) {
    			unlink($uploadDir . $profile_pic);
    		}
    		$em = "Terjadi kesalahan saat membuat akun";
    		header("Location: ../signup.php?error=" . urlencode($em) . "&$data");
    		exit;
    	}
    }

} else {
	$em = "Semua field harus diisi";
	header("Location: ../signup.php?error=" . urlencode($em));
	exit;
}
