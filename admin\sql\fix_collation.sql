-- Fix collation mismatch issues
-- Convert all tables to use utf8mb4_general_ci consistently

-- Fix jabatan table
ALTER TABLE `jabatan` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- Fix pengurus table if exists
ALTER TABLE `pengurus` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- Fix users table if exists
ALTER TABLE `users` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- Fix posts table if exists
ALTER TABLE `posts` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- Fix post table if exists (alternative name)
ALTER TABLE `post` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- Fix categories table if exists
ALTER TABLE `categories` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- Fix category table if exists (alternative name)
ALTER TABLE `category` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- Fix comments table if exists
ALTER TABLE `comments` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- Fix comment table if exists (alternative name)
ALTER TABLE `comment` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- Recreate jabatan table with correct collation
DROP TABLE IF EXISTS `jabatan`;
CREATE TABLE `jabatan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nama_jabatan` varchar(50) NOT NULL,
  `deskripsi` text DEFAULT NULL,
  `urutan` int(3) DEFAULT 1,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nama_jabatan` (`nama_jabatan`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Insert default jabatan data
INSERT INTO `jabatan` (`nama_jabatan`, `deskripsi`, `urutan`, `is_active`) VALUES
('Ketua', 'Ketua UKM Panahan Gendewa Geni', 1, 1),
('Wakil Ketua', 'Wakil Ketua UKM Panahan Gendewa Geni', 2, 1),
('Sekretaris', 'Sekretaris UKM Panahan Gendewa Geni', 3, 1),
('Bendahara', 'Bendahara UKM Panahan Gendewa Geni', 4, 1),
('Koordinator Divisi Latihan', 'Koordinator untuk divisi latihan dan pelatihan', 5, 1),
('Koordinator Divisi Event', 'Koordinator untuk divisi acara dan event', 6, 1),
('Koordinator Divisi Humas', 'Koordinator untuk divisi hubungan masyarakat', 7, 1),
('Anggota', 'Anggota UKM Panahan Gendewa Geni', 8, 1);
