<?php
// Handle form submission
$errors = [];
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $judul = trim($_POST['judul'] ?? '');
    $deskripsi = trim($_POST['deskripsi'] ?? '');
    $kategori = trim($_POST['kategori'] ?? '');

    // Validation
    if (empty($judul)) {
        $errors[] = 'Judul tidak boleh kosong';
    } elseif (strlen($judul) < 3) {
        $errors[] = 'Judul minimal 3 karakter';
    }

    if (empty($deskripsi)) {
        $errors[] = 'Deskripsi tidak boleh kosong';
    }

    // Handle file upload
    $gambar = '';
    if (isset($_FILES['gambar']) && $_FILES['gambar']['error'] === UPLOAD_ERR_OK) {
        $allowedTypes = [
            'image/jpeg', 'image/png', 'image/gif', 'image/webp',
            'video/mp4', 'video/webm', 'video/ogg'
        ];
        $maxSize = 50 * 1024 * 1024; // 50MB

        if (!in_array($_FILES['gambar']['type'], $allowedTypes)) {
            $errors[] = 'Tipe file tidak diizinkan. Gunakan JPG, PNG, GIF, WebP, MP4, WebM, atau OGG';
        } elseif ($_FILES['gambar']['size'] > $maxSize) {
            $errors[] = 'Ukuran file terlalu besar. Maksimal 50MB';
        } else {
            $uploadDir = __DIR__ . "/../upload/galeri/";
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }

            $fileExtension = pathinfo($_FILES['gambar']['name'], PATHINFO_EXTENSION);
            $gambar = 'galeri_' . time() . '_' . uniqid() . '.' . $fileExtension;

            if (!move_uploaded_file($_FILES['gambar']['tmp_name'], $uploadDir . $gambar)) {
                $errors[] = 'Gagal mengupload file';
                $gambar = '';
            }
        }
    } else {
        $errors[] = 'File gambar/video harus diupload';
    }

    // Insert to database
    if (empty($errors)) {
        include_once __DIR__ . "/data/galeri.php";
        include_once __DIR__ . '/inc/header.php'; // for DB connection/session if needed
        try {
            if (addGaleri($conn, $judul, $gambar, $deskripsi, $kategori)) {
                echo "<script>window.location.href='galeri.php?success=" . urlencode("Item galeri berhasil ditambahkan") . "';</script>";
                exit();
            } else {
                $errors[] = 'Gagal menyimpan ke database';
            }
        } catch (Exception $e) {
            $errors[] = 'Error: ' . $e->getMessage();
        }
    }
}

// Include header (sudah ada session check dan DB connection)
if (!isset($conn)) {
    include __DIR__ . '/inc/header.php';
}
?>
<div class="dashboard-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="welcome-header">
            <i class="fa fa-plus mr-3"></i>Tambah Item Galeri
        </h1>
        <a href="galeri.php" class="btn btn-secondary">
            <i class="fa fa-arrow-left mr-2"></i>Kembali
        </a>
    </div>

    <?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <i class="fa fa-exclamation-circle mr-2"></i>
        <ul class="mb-0">
            <?php foreach ($errors as $error): ?>
            <li><?= htmlspecialchars($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php endif; ?>

    <div class="card summary-card">
        <div class="card-header card-header-brand-info">
            <i class="fa fa-plus mr-2"></i>Form Tambah Galeri
        </div>
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group">
                            <label for="judul"><i class="fa fa-file-text mr-1"></i>Judul *</label>
                            <input type="text" class="form-control" id="judul" name="judul"
                                   value="<?= htmlspecialchars($_POST['judul'] ?? '') ?>"
                                   placeholder="Masukkan judul untuk item galeri" required>
                            <small class="form-text text-muted">Minimal 3 karakter</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="kategori"><i class="fa fa-tag mr-1"></i>Kategori</label>
                            <select class="form-control" id="kategori" name="kategori">
                                <option value="">Pilih Kategori</option>
                                <option value="Kegiatan" <?= ($_POST['kategori'] ?? '') == 'Kegiatan' ? 'selected' : '' ?>>Kegiatan</option>
                                <option value="Latihan" <?= ($_POST['kategori'] ?? '') == 'Latihan' ? 'selected' : '' ?>>Latihan</option>
                                <option value="Kompetisi" <?= ($_POST['kategori'] ?? '') == 'Kompetisi' ? 'selected' : '' ?>>Kompetisi</option>
                                <option value="Event" <?= ($_POST['kategori'] ?? '') == 'Event' ? 'selected' : '' ?>>Event</option>
                                <option value="Lainnya" <?= ($_POST['kategori'] ?? '') == 'Lainnya' ? 'selected' : '' ?>>Lainnya</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="deskripsi"><i class="fa fa-info-circle mr-1"></i>Deskripsi *</label>
                    <textarea class="form-control" id="deskripsi" name="deskripsi" rows="3"
                              placeholder="Deskripsi tentang gambar/video ini" required><?= htmlspecialchars($_POST['deskripsi'] ?? '') ?></textarea>
                    <small class="form-text text-muted">Jelaskan tentang konten yang diupload</small>
                </div>

                <div class="form-group">
                    <label for="gambar"><i class="fa fa-upload mr-1"></i>File Media *</label>
                    <div class="custom-file">
                        <input type="file" class="custom-file-input" id="gambar" name="gambar"
                               accept="image/*,video/*" required>
                        <label class="custom-file-label" for="gambar">Pilih gambar atau video...</label>
                    </div>
                    <small class="form-text text-muted">
                        <strong>Gambar:</strong> JPG, PNG, GIF, WebP (max 10MB)<br>
                        <strong>Video:</strong> MP4, WebM, OGG (max 50MB)
                    </small>

                    <div id="filePreview" class="mt-3" style="display: none;">
                        <div id="imagePreview" style="display: none;">
                            <label class="text-muted">Preview Gambar:</label><br>
                            <img id="previewImg" src="" alt="Preview" style="max-width: 300px; max-height: 200px; border-radius: 10px; border: 2px solid #17a2b8;">
                        </div>
                        <div id="videoPreview" style="display: none;">
                            <label class="text-muted">Preview Video:</label><br>
                            <video id="previewVideo" controls style="max-width: 300px; max-height: 200px; border-radius: 10px; border: 2px solid #17a2b8;">
                                Your browser does not support the video tag.
                            </video>
                        </div>
                        <div id="fileInfo" class="mt-2">
                            <small class="text-muted">
                                <strong>Nama File:</strong> <span id="fileName"></span><br>
                                <strong>Ukuran:</strong> <span id="fileSize"></span><br>
                                <strong>Tipe:</strong> <span id="fileType"></span>
                            </small>
                        </div>
                    </div>
                </div>

                <div class="form-group text-right">
                    <a href="galeri.php" class="btn btn-secondary mr-2">
                        <i class="fa fa-times mr-1"></i>Batal
                    </a>
                    <button type="submit" class="btn btn-brand-info">
                        <i class="fa fa-save mr-1"></i>Simpan ke Galeri
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="../js/jquery-1.12.4.min.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script>
$(document).ready(function() {
    // Custom file input
    $('.custom-file-input').on('change', function() {
        var fileName = $(this).val().split('\\').pop();
        $(this).next('.custom-file-label').html(fileName);

        // File preview
        if (this.files && this.files[0]) {
            var file = this.files[0];
            var fileSize = (file.size / 1024 / 1024).toFixed(2) + ' MB';
            var fileType = file.type;

            $('#fileName').text(file.name);
            $('#fileSize').text(fileSize);
            $('#fileType').text(fileType);

            if (file.type.startsWith('image/')) {
                var reader = new FileReader();
                reader.onload = function(e) {
                    $('#previewImg').attr('src', e.target.result);
                    $('#imagePreview').show();
                    $('#videoPreview').hide();
                    $('#filePreview').show();
                }
                reader.readAsDataURL(file);
            } else if (file.type.startsWith('video/')) {
                var url = URL.createObjectURL(file);
                $('#previewVideo').attr('src', url);
                $('#videoPreview').show();
                $('#imagePreview').hide();
                $('#filePreview').show();
            }
        }
    });

    // Auto-resize textarea
    $('textarea').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });

    // Form validation
    $('form').on('submit', function(e) {
        var isValid = true;

        // Check required fields
        $(this).find('input[required], textarea[required]').each(function() {
            if (!$(this).val().trim()) {
                $(this).addClass('is-invalid');
                isValid = false;
            } else {
                $(this).removeClass('is-invalid');
            }
        });

        // Check file
        var fileInput = $('#gambar')[0];
        if (!fileInput.files || fileInput.files.length === 0) {
            $('#gambar').addClass('is-invalid');
            isValid = false;
        } else {
            $('#gambar').removeClass('is-invalid');
        }

        if (!isValid) {
            e.preventDefault();
            alert('Mohon lengkapi semua field yang wajib diisi!');
        }
    });
});
</script>

</body>
</html>
