<?php
?>

<!-- Google Web Fonts -->
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link
    href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Teko:wght@300..700&display=swap"
    rel="stylesheet">

<link rel="stylesheet" href="css/slick.css">

<!--====== Animate css ======-->
<link rel="stylesheet" href="css/animate.css">

<!--====== Nice Select css ======-->
<link rel="stylesheet" href="css/nice-select.css">

<!--====== Nice Number css ======-->
<link rel="stylesheet" href="css/jquery.nice-number.min.css">

<!--====== Magnific Popup css ======-->
<link rel="stylesheet" href="css/magnific-popup.css">

<!--====== Bootstrap css ======-->
<link rel="stylesheet" href="css/bootstrap.min.css">

<!--====== Fontawesome css ======-->
<link rel="stylesheet" href="css/font-awesome.min.css">

<!--====== Default css ======-->
<link rel="stylesheet" href="css/default.css">

<!--====== Style css ======-->
<link rel="stylesheet" href="css/style.css">

<!--====== Responsive css ======-->
<link rel="stylesheet" href="css/responsive.css">


<header id="header-part">
    <div class="navigation navigation-2">
        <div class="container-fluid">
            <nav class="navbar navbar-expand-lg navbar-dark">
                <a class="navbar-brand" href="index.php">
                    <img src="images/logo.png" alt="Logo" class="navbar-logo">
                </a>

                <button class="navbar-toggler custom-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarSupportedContent">
                    <ul class="navbar-nav ml-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="index.php">
                                <i class="fa fa-home d-md-none me-2"></i>Beranda
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="category.php">
                                <i class="fa fa-newspaper-o d-md-none me-2"></i>Berita
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="about.php">
                                <i class="fa fa-users d-md-none me-2"></i>Tim Gendewa Geni
                            </a>
                        </li>

                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="tentangDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="fa fa-info-circle d-md-none me-2"></i>Tentang Kami
                            </a>
                            <div class="dropdown-menu dropdown-menu-custom" aria-labelledby="tentangDropdown">
                                <a class="dropdown-item" href="galeri.php">
                                    <i class="fa fa-camera me-2"></i>Galeri
                                </a>
                                <a class="dropdown-item" href="peraturan.php">
                                    <i class="fa fa-file-text me-2"></i>Peraturan SOP
                                </a>
                                <a class="dropdown-item" href="contact.php">
                                    <i class="fa fa-envelope me-2"></i>Kontak Kami
                                </a>
                            </div>
                        </li>
                        <?php if (!isset($_SESSION['username'])) { ?>
                            <li class="nav-item">
                                <a href="login.php" class="nav-link btn-login-mobile">
                                    <i class="fa fa-sign-in d-md-none me-2"></i>LOGIN
                                </a>
                            </li>
                        <?php } ?>

                        <?php if (isset($_SESSION['username'])) { ?>
                            <li class="nav-item dropdown user-dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <img src="<?php echo isset($_SESSION['foto']) && $_SESSION['foto'] ? htmlspecialchars($_SESSION['foto']) : 'upload/user_default.png'; ?>" alt="Foto Profil" class="user-avatar">
                                    <span class="user-name">Hi, <?php echo htmlspecialchars($_SESSION['username']); ?></span>
                                </a>
                                <div class="dropdown-menu dropdown-menu-right dropdown-menu-custom" aria-labelledby="navbarDropdown">
                                    <a class="dropdown-item" href="profil.php">
                                        <i class="fa fa-user me-2"></i>Profil
                                    </a>
                                    <a class="dropdown-item" href="php/logout.php">
                                        <i class="fa fa-sign-out me-2"></i>Logout
                                    </a>
                                </div>
                            </li>
                        <?php } ?>
                    </ul>
                </div>
            </nav>
        </div>
    </div>
</header>
<!--====== jquery js ======-->
<script src="js/vendor/modernizr-3.6.0.min.js"></script>
<script src="js/vendor/jquery-1.12.4.min.js"></script>

<!--====== Popper js ======-->
<script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
<!--====== Bootstrap js ======-->
<script src="js/bootstrap.min.js"></script>

<!--====== Slick js ======-->
<script src="js/slick.min.js"></script>

<!--====== Magnific Popup js ======-->
<script src="js/jquery.magnific-popup.min.js"></script>

<!--====== Counter Up js ======-->
<script src="js/waypoints.min.js"></script>
<script src="js/jquery.counterup.min.js"></script>

<!--====== Nice Select js ======-->
<script src="js/jquery.nice-select.min.js"></script>

<!--====== Nice Number js ======-->
<script src="js/jquery.nice-number.min.js"></script>

<!--====== Count Down js ======-->
<script src="js/jquery.countdown.min.js"></script>

<!--====== Validator js ======-->
<script src="js/validator.min.js"></script>

<!--====== Ajax Contact js ======-->
<script src="js/ajax-contact.js"></script>

<!--====== Main js ======-->
<script src="js/main.js"></script>

<!--====== Map js ======-->
<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDC3Ip9iVC0nIxC6V14CKLQ1HZNF_65qEQ"></script>
<script src="js/map-script.js"></script>
</header>
<style>
/* Main Navigation Styles */
.navigation.navigation-2 {
    background: linear-gradient(90deg, #111 0%, #005a99 50%, #ff9800 100%) !important;
    padding: 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.navigation-2 .navbar {
    padding: 12px 0;
    background: transparent !important;
}

.navigation-2 .navbar-brand {
    padding: 0;
}

.navbar-logo {
    height: 45px;
    width: auto;
    transition: transform 0.3s ease;
}

.navbar-logo:hover {
    transform: scale(1.05);
}

/* Navigation Links */
.navigation-2 .navbar-nav .nav-link {
    color: #fff !important;
    font-weight: 500;
    font-size: 15px;
    padding: 8px 16px !important;
    margin: 0 4px;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
}

.navigation-2 .navbar-nav .nav-link:hover {
    background: rgba(255,255,255,0.1);
    transform: translateY(-2px);
}

.navigation-2 .navbar-nav .nav-link.active {
    background: rgba(255,136,0,0.3);
}

/* Custom Toggler */
.custom-toggler {
    border: 2px solid #fff;
    border-radius: 8px;
    padding: 6px 10px;
}

.custom-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25);
}

.custom-toggler .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Dropdown Menus with Cool Animations */
.dropdown-menu-custom {
    background: linear-gradient(135deg, #005a99 0%, #ff9800 100%) !important;
    border: none !important;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1), 0 5px 15px rgba(0,0,0,0.07);
    margin-top: 12px;
    min-width: 220px;
    opacity: 0;
    transform: translateY(-20px) scale(0.95) rotateX(-10deg);
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    visibility: hidden;
    transform-origin: top center;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

/* Cool entrance animation */
.dropdown.show .dropdown-menu-custom,
.nav-item.dropdown.show .dropdown-menu-custom {
    opacity: 1;
    transform: translateY(0) scale(1) rotateX(0deg);
    visibility: visible;
    animation: dropdownSlideIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Keyframe animations */
@keyframes dropdownSlideIn {
    0% {
        opacity: 0;
        transform: translateY(-30px) scale(0.8) rotateX(-15deg);
    }
    50% {
        opacity: 0.8;
        transform: translateY(5px) scale(1.02) rotateX(2deg);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1) rotateX(0deg);
    }
}

/* Glowing border effect */
.dropdown-menu-custom::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #005a99, #ff9800, #005a99);
    border-radius: 17px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.nav-item.dropdown.show .dropdown-menu-custom::before {
    opacity: 0.3;
    animation: borderGlow 2s ease-in-out infinite alternate;
}

@keyframes borderGlow {
    0% { opacity: 0.3; }
    100% { opacity: 0.6; }
}

/* Enhanced dropdown items */
.dropdown-menu-custom .dropdown-item {
    color: #fff !important;
    padding: 14px 20px;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    border-radius: 10px;
    margin: 6px 10px;
    position: relative;
    overflow: hidden;
}

/* Enhanced hover effects */
.dropdown-menu-custom .dropdown-item:hover {
    background: rgba(255,255,255,0.25) !important;
    transform: translateX(8px) scale(1.02);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.dropdown-menu-custom .dropdown-item i {
    width: 20px;
    text-align: center;
    transition: transform 0.3s ease;
}

.dropdown-menu-custom .dropdown-item:hover i {
    transform: scale(1.2) rotate(5deg);
}

/* User Profile */
.user-dropdown .nav-link {
    display: flex;
    align-items: center;
    padding: 6px 12px !important;
}

.user-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #fff;
    margin-right: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.user-name {
    font-size: 14px;
    font-weight: 500;
}

/* Login Button */
.btn-login-mobile {
    background: linear-gradient(45deg, #ff9800, #ff6b35) !important;
    border-radius: 25px !important;
    font-weight: 600 !important;
    padding: 8px 20px !important;
    margin: 0 8px !important;
    transition: all 0.3s ease !important;
}

.btn-login-mobile:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(255,152,0,0.4) !important;
}

/* Mobile Responsive Styles */
@media (max-width: 991px) {
    .navigation-2 .navbar {
        padding: 8px 0;
    }

    .navbar-logo {
        height: 40px;
    }

    .navbar-collapse {
        background: rgba(0,0,0,0.95);
        margin-top: 15px;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.3);
    }

    .navigation-2 .navbar-nav .nav-link {
        padding: 12px 20px !important;
        margin: 4px 0;
        border-radius: 8px;
        font-size: 16px;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }

    .navigation-2 .navbar-nav .nav-link:last-child {
        border-bottom: none;
    }

    .dropdown-menu-custom {
        position: static !important;
        transform: none !important;
        opacity: 1 !important;
        visibility: visible !important;
        background: rgba(255,255,255,0.1) !important;
        box-shadow: none !important;
        margin: 8px 0 0 0 !important;
        border-radius: 8px;
    }

    .dropdown-menu-custom .dropdown-item {
        padding: 10px 20px;
        margin: 2px 0;
        font-size: 15px;
    }

    .user-dropdown .nav-link {
        justify-content: flex-start;
        padding: 15px 20px !important;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        margin-right: 12px;
    }

    .user-name {
        font-size: 16px;
    }

    .btn-login-mobile {
        text-align: center !important;
        margin: 8px 0 !important;
    }
}

@media (max-width: 576px) {
    .navbar-logo {
        height: 35px;
    }

    .navbar-collapse {
        margin-top: 10px;
        padding: 15px;
    }

    .navigation-2 .navbar-nav .nav-link {
        font-size: 15px;
        padding: 10px 15px !important;
    }

    .user-name {
        font-size: 15px;
    }
}

/* Animation for mobile menu */
.navbar-collapse.collapsing {
    transition: height 0.35s ease;
}

.navbar-collapse.show {
    animation: slideDown 0.35s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
<script>
$(document).ready(function() {
    // Simple and effective dropdown handling
    function initDropdowns() {
        // Remove any existing event handlers
        $('.nav-item.dropdown .dropdown-toggle').off('click.customDropdown');
        $('.nav-item.dropdown').off('mouseenter.customDropdown mouseleave.customDropdown');

        if ($(window).width() >= 992) {
            // Desktop: Hover to show dropdown with improved timing
            $('.nav-item.dropdown').on('mouseenter.customDropdown', function() {
                var $dropdown = $(this);
                var $menu = $dropdown.find('.dropdown-menu-custom');

                // Clear any existing hide timeout
                clearTimeout($dropdown.data('hideTimeout'));

                // Close other dropdowns first
                $('.nav-item.dropdown').not($dropdown).removeClass('show').find('.dropdown-menu-custom').removeClass('show');

                // Show current dropdown immediately
                $dropdown.addClass('show');
                $menu.addClass('show');

            }).on('mouseleave.customDropdown', function() {
                var $dropdown = $(this);
                var $menu = $dropdown.find('.dropdown-menu-custom');

                // Set a longer timeout before hiding (800ms instead of immediate)
                var hideTimeout = setTimeout(function() {
                    $dropdown.removeClass('show');
                    $menu.removeClass('show');
                }, 800);

                // Store timeout reference for clearing
                $dropdown.data('hideTimeout', hideTimeout);
            });

            // Also add hover events to dropdown menu itself to prevent hiding
            $('.dropdown-menu-custom').on('mouseenter.customDropdown', function() {
                var $dropdown = $(this).closest('.nav-item.dropdown');
                // Clear hide timeout when mouse enters dropdown menu
                clearTimeout($dropdown.data('hideTimeout'));
            }).on('mouseleave.customDropdown', function() {
                var $dropdown = $(this).closest('.nav-item.dropdown');
                var $menu = $(this);

                // Set timeout when mouse leaves dropdown menu
                var hideTimeout = setTimeout(function() {
                    $dropdown.removeClass('show');
                    $menu.removeClass('show');
                }, 500);

                $dropdown.data('hideTimeout', hideTimeout);
            });

            // Also handle click for desktop with timeout management
            $('.nav-item.dropdown .dropdown-toggle').on('click.customDropdown', function(e) {
                e.preventDefault();
                var $dropdown = $(this).parent();
                var $menu = $dropdown.find('.dropdown-menu-custom');

                // Clear any existing timeout
                clearTimeout($dropdown.data('hideTimeout'));

                if ($dropdown.hasClass('show')) {
                    $dropdown.removeClass('show');
                    $menu.removeClass('show');
                } else {
                    // Close other dropdowns
                    $('.nav-item.dropdown').removeClass('show').find('.dropdown-menu-custom').removeClass('show');
                    // Open this dropdown
                    $dropdown.addClass('show');
                    $menu.addClass('show');
                }
            });
        } else {
            // Mobile: Click to toggle dropdown
            $('.nav-item.dropdown .dropdown-toggle').on('click.customDropdown', function(e) {
                e.preventDefault();
                e.stopPropagation();

                var $dropdown = $(this).parent();
                var $menu = $dropdown.find('.dropdown-menu-custom');

                // Close other dropdowns
                $('.nav-item.dropdown').not($dropdown).removeClass('show');
                $('.dropdown-menu-custom').not($menu).slideUp(200);

                // Toggle current dropdown
                if ($dropdown.hasClass('show')) {
                    $dropdown.removeClass('show');
                    $menu.slideUp(200);
                } else {
                    $dropdown.addClass('show');
                    $menu.slideDown(200);
                }
            });
        }
    }

    // Initialize dropdowns
    initDropdowns();

    // Re-initialize on window resize
    $(window).resize(function() {
        initDropdowns();
    });

    // Mobile menu enhancements
    $('.navbar-toggler').on('click', function() {
        $(this).toggleClass('active');
    });

    // Close mobile menu when clicking on regular nav links
    $('.navbar-nav .nav-link:not(.dropdown-toggle)').on('click', function() {
        if ($(window).width() < 992) {
            $('.navbar-collapse').collapse('hide');
            $('.navbar-toggler').removeClass('active');
        }
    });

    // Active page highlighting
    var currentPage = window.location.pathname.split('/').pop();
    $('.navbar-nav .nav-link').each(function() {
        var linkPage = $(this).attr('href');
        if (linkPage === currentPage || (currentPage === '' && linkPage === 'index.php')) {
            $(this).addClass('active');
        }
    });

    // Smooth scroll for anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 80
            }, 1000);
        }
    });

    // Navbar background on scroll
    $(window).scroll(function() {
        if ($(this).scrollTop() > 50) {
            $('.navigation-2').addClass('scrolled');
        } else {
            $('.navigation-2').removeClass('scrolled');
        }
    });

    // Add cool hover effects to dropdown items
    $('.dropdown-menu-custom .dropdown-item').hover(
        function() {
            $(this).addClass('animate__animated animate__pulse');
        },
        function() {
            $(this).removeClass('animate__animated animate__pulse');
        }
    );

    // Add ripple effect on dropdown item click
    $('.dropdown-menu-custom .dropdown-item').on('click', function(e) {
        var $this = $(this);
        var $ripple = $('<span class="ripple"></span>');

        $this.append($ripple);

        var btnOffset = $this.offset();
        var xPos = e.pageX - btnOffset.left;
        var yPos = e.pageY - btnOffset.top;

        $ripple.css({
            top: yPos,
            left: xPos
        });

        setTimeout(function() {
            $ripple.remove();
        }, 600);
    });



    // Prevent dropdown from closing when clicking inside dropdown menu
    $('.dropdown-menu-custom').on('click', function(e) {
        e.stopPropagation();
    });

    // Close dropdown when clicking outside (with improved timing)
    $(document).on('click', function(e) {
        if (!$(e.target).closest('.nav-item.dropdown').length) {
            // Add small delay to prevent accidental closing
            setTimeout(function() {
                $('.nav-item.dropdown').each(function() {
                    var $dropdown = $(this);
                    clearTimeout($dropdown.data('hideTimeout'));
                    $dropdown.removeClass('show');
                    $dropdown.find('.dropdown-menu-custom').removeClass('show');
                });
            }, 150);
        }
    });
});

// Add scrolled class styles
$('<style>')
    .prop('type', 'text/css')
    .html(`
        .navigation-2.scrolled {
            background: linear-gradient(90deg, rgba(17,17,17,0.95) 0%, rgba(0,90,153,0.95) 50%, rgba(255,152,0,0.95) 100%) !important;
            backdrop-filter: blur(10px);
        }
    `)
    .appendTo('head');
</script>