<?php
session_start();
$logged = false;
if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
    $logged = true;
    $user_id = $_SESSION['user_id'];
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>UKM Panahan Universitas Semarang</title>
    <link rel="icon" href="img/logo.jpg" type="Image/x-icon">
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="description" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/font-awesome.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <style>
body {
    background: #fff !important;
    min-height: 100vh;
}
.singel-publication {
    background: #fff;
    border-radius: 1.2rem;
    box-shadow: 0 4px 24px 0 rgba(46, 54, 80, 0.08);
    margin-bottom: 24px;
    overflow: hidden;
    transition: box-shadow 0.2s, transform 0.2s;
}
.singel-publication:hover {
    box-shadow: 0 8px 32px 0 rgba(46, 54, 80, 0.16);
    transform: translateY(-4px) scale(1.02);
}
.singel-publication .image img {
    width: 100%;
    height: 220px;
    object-fit: cover;
    border-radius: 1.2rem 1.2rem 0 0;
}
.singel-publication .cont {
    padding: 18px 10px 12px 10px;
}
.singel-publication .name h6 {
    font-size: 1.1rem;
    font-weight: 700;
    color: #005a99;
    margin-bottom: 2px;
}
.singel-publication .name span {
    font-size: 0.98rem;
}
.singel-publication .badge {
    background: linear-gradient(90deg, #005a99 0%, #ff9800 100%) !important;
    color: #fff !important;
    border-radius: 1rem;
    font-size: 0.92em;
    font-weight: 500;
    margin-bottom: 0.5em;
}
.singel-publication .btn-primary.btn-sm-square {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2em;
    margin: 8px auto 0 auto;
}
/* Responsive header/navbar */
@media (max-width: 991px) {
    .navbar-nav { flex-direction: column !important; align-items: flex-start !important; }
    .navbar-nav .nav-item { margin-left: 0 !important; margin-bottom: 8px; }
    .navbar-brand img { max-width: 120px; height: auto; }
    footer, .footer { text-align: center !important; padding: 18px 6px !important; font-size: 0.98rem; }

    /* Hero section responsive */
    .pt-120 { padding-top: 80px !important; }
    .pb-120 { padding-bottom: 80px !important; }
    h1 { font-size: 2rem !important; }
    .lead { font-size: 1.1rem !important; }

    /* Section spacing */
    .pt-80 { padding-top: 60px !important; }
    .pb-80 { padding-bottom: 60px !important; }
}

@media (max-width: 767px) {
    .singel-publication .image img {
        height: 180px;
    }
    .singel-publication .cont {
        padding: 15px 10px 12px 10px;
    }
    .singel-publication .name h6 {
        font-size: 1rem;
    }
    .singel-publication .btn-primary.btn-sm-square {
        width: 30px;
        height: 30px;
        font-size: 1.1em;
    }
    .navbar-brand img {
        max-width: 90px;
    }

    /* Hero section mobile */
    h1 { font-size: 1.8rem !important; }
    .lead { font-size: 1rem !important; }

    /* Cards responsive */
    .card-body { padding: 1.5rem !important; }
    .pt-80 { padding-top: 50px !important; }
    .pb-80 { padding-bottom: 50px !important; }

    /* Statistics cards */
    .col-lg-3.col-md-6 { margin-bottom: 1.5rem; }
}

@media (max-width: 575px) {
    .singel-publication {
        border-radius: 0.8rem;
        margin-bottom: 1.5rem;
    }
    .singel-publication .image img {
        border-radius: 0.8rem 0.8rem 0 0;
        height: 160px;
    }
    .singel-publication .cont {
        padding: 12px 8px 10px 8px;
    }
    .singel-publication .name h6 {
        font-size: 0.95rem;
    }
    .singel-publication .badge {
        font-size: 0.8em;
    }
    .navbar-brand img {
        max-width: 70px;
    }
    .footer, footer {
        font-size: 0.92rem;
        padding: 12px 2px !important;
    }

    /* Hero section extra small */
    h1 { font-size: 1.6rem !important; }
    .lead { font-size: 0.95rem !important; }

    /* Section spacing mobile */
    .pt-80 { padding-top: 40px !important; }
    .pb-80 { padding-bottom: 40px !important; }
    .pt-120 { padding-top: 60px !important; }
    .pb-120 { padding-bottom: 60px !important; }

    /* Cards mobile */
    .card-body { padding: 1.25rem !important; }
    .mb-5 { margin-bottom: 2rem !important; }

    /* Contact cards */
    .col-lg-4.col-md-6 { margin-bottom: 1.5rem; }
}
.back-to-top .btn {
    background: linear-gradient(90deg, #005a99 0%, #ff9800 100%) !important;
    color: #fff !important;
    border-radius: 50%;
    width: 38px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2em;
    box-shadow: 0 2px 8px 0 rgba(10,41,71,0.10);
    transition: background 0.2s;
}
.back-to-top .btn:hover {
    background: linear-gradient(90deg, #ff9800 0%, #005a99 100%) !important;
}
</style>
<!-- ...existing code... -->
<?php
    include 'inc/navbar.php';
    include_once("admin/data/pengurus.php");
    include_once("admin/data/divisi.php");
    include_once("admin/data/post.php");
    include_once("admin/data/galeri.php");
    include_once("admin/data/user.php");
    include_once("db_conn.php");

    // Get data from database
    $penguruss = function_exists('getAllPengurus') ? getAllPengurus($conn) : [];

    // Get statistics
    $stats = [
        'pengurus' => count($penguruss),
        'posts' => 0,
        'galeri' => 0,
        'members' => 0
    ];

    // Get posts count
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM post");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats['posts'] = $result['count'];
    } catch (Exception $e) {
        $stats['posts'] = 0;
    }

    // Get gallery count
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM galeri");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats['galeri'] = $result['count'];
    } catch (Exception $e) {
        $stats['galeri'] = 0;
    }

    // Get members count
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM users");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $stats['members'] = $result['count'];
    } catch (Exception $e) {
        $stats['members'] = 0;
    }
    ?>
   <div class="preloader">
        <div class="loader rubix-cube">
            <div class="layer layer-1"></div>
            <div class="layer layer-2"></div>
            <div class="layer layer-3 color-1"></div>
            <div class="layer layer-4"></div>
            <div class="layer layer-5"></div>
            <div class="layer layer-6"></div>
            <div class="layer layer-7"></div>
            <div class="layer layer-8"></div>
        </div>
    </div>

    <!-- Header End -->

    <!-- About Hero Section -->
    <section class="pt-120 pb-60" style="background: linear-gradient(rgba(0,90,153,0.8), rgba(255,152,0,0.8)), url('upload/services-img.jpg'); background-size: cover; background-position: center; background-repeat: no-repeat;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="text-white mb-4" style="font-size: 2.5rem; font-weight: 700; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                        <i class="fa fa-bullseye mr-3"></i>TIM Pengurus UKM Panahan Gendewa Geni
                    </h1>
                </div>
            </div>
        </div>
    </section>

    <!-- Vision & Mission Section -->
  
    <!-- Organization Structure Section -->
    <section id="pengurus-section" class="pt-80 pb-80">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="text-center mb-5">
                        <h2 class="text-primary font-weight-bold mb-3">
                            <i class="fa fa-users mr-3"></i>Struktur Kepengurusan
                        </h2>
                        <p class="text-muted lead">Tim pengurus UKM Panahan Gendewa Geni periode 2024/2025</p>
                        <div class="d-inline-block" style="width: 60px; height: 4px; background: linear-gradient(90deg, #005a99 0%, #ff9800 100%); border-radius: 2px;"></div>
                    </div>
                </div>
            </div>

            <?php if ($penguruss && count($penguruss) > 0) { ?>
            <div class="row justify-content-center">
                <?php foreach ($penguruss as $pengurus) {
                    $divisi = function_exists('getdivisiById') ? getdivisiById($conn, $pengurus['divisi']) : null;
                ?>
                <div class="col-lg-3 col-md-6 col-sm-8 mb-4">
                    <div class="singel-publication">
                        <div class="image">
                            <img src="<?= !empty($pengurus['foto']) ? 'upload/' . htmlspecialchars($pengurus['foto']) : 'upload/blog/default.jpg' ?>"
                                 alt="<?= isset($pengurus['nama']) ? htmlspecialchars($pengurus['nama']) : 'Pengurus' ?>"
                                 onerror="this.onerror=null;this.src='upload/blog/default.jpg';">
                            <?php if (!empty($pengurus['instagram'])): ?>
                            <div class="row justify-content-center">
                                <a href="<?= htmlspecialchars($pengurus['instagram']) ?>"
                                   class="btn btn-primary btn-sm-square"
                                   target="_blank" rel="noopener" title="Instagram">
                                    <i class="fa fa-instagram"></i>
                                </a>
                            </div>
                            <?php endif; ?>
                        </div>
                        <div class="cont">
                            <div class="name text-center">
                                <h6 class="mb-1"><?= isset($pengurus['nama']) ? htmlspecialchars($pengurus['nama']) : '-' ?></h6>
                                <span class="d-block mb-2" style="font-size:1.05em; color:#555; font-weight:500;">
                                    <?= isset($pengurus['jabatan']) ? htmlspecialchars($pengurus['jabatan']) : '-' ?>
                                </span>
                                <?php if ($divisi && isset($divisi['divisi'])): ?>
                                    <span class="badge mb-1" style="background: linear-gradient(90deg, #005a99 0%, #ff9800 100%); color: white;">
                                        <?= htmlspecialchars($divisi['divisi']) ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php } ?>
            </div>
            <?php } else { ?>
            <div class="row">
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fa fa-users" style="font-size: 4rem; color: #ddd;"></i>
                        <h4 class="mt-3 text-muted">Data pengurus sedang diperbarui</h4>
                        <p class="text-muted">Informasi struktur kepengurusan akan segera tersedia.</p>
                    </div>
                </div>
            </div>
            <?php } ?>
        </div>
    </section>

 
    <!-- Footer Start -->
    <?php   include 'inc/footer.php'; ?>
    <!-- Footer End -->
    <script src="js/jquery-1.12.4.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/main.js"></script>
    <!-- Back to Top -->
    <div class="back-to-top">
        <a href="#" class="btn"><i class="fa fa-arrow-up"></i></a>
    </div>


 
</body>

</html>