<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header('Location: ../login.php');
    exit();
}

// Include database connection and data handler
include_once __DIR__ . '/../db_conn.php';
include_once 'data/pengurus.php';

// Check if ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: pengurus.php?error=' . urlencode('ID pengurus tidak valid'));
    exit();
}

$pengurus_id = intval($_GET['id']);

try {
    // Check if pengurus exists and get photo info
    $stmt = $conn->prepare("SELECT * FROM pengurus WHERE id = ?");
    $stmt->execute([$pengurus_id]);
    $pengurus = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$pengurus) {
        header('Location: pengurus.php?error=' . urlencode('Pengurus tidak ditemukan'));
        exit();
    }

    // Delete photo file if exists
    if (!empty($pengurus['foto']) && file_exists(__DIR__ . '/../upload/' . $pengurus['foto'])) {
        unlink(__DIR__ . '/../upload/' . $pengurus['foto']);
    }

    // Delete pengurus using data handler function
    if (function_exists('deletePengurus')) {
        $result = deletePengurus($conn, $pengurus_id);
    } else {
        // Fallback to direct database query
        $stmt = $conn->prepare("DELETE FROM pengurus WHERE id = ?");
        $result = $stmt->execute([$pengurus_id]);
    }

    if ($result) {
        header('Location: pengurus.php?success=' . urlencode('Pengurus berhasil dihapus'));
    } else {
        header('Location: pengurus.php?error=' . urlencode('Gagal menghapus pengurus'));
    }

} catch (Exception $e) {
    header('Location: pengurus.php?error=' . urlencode('Error: ' . $e->getMessage()));
}

exit();
