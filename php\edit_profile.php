<?php
session_start();
if (!isset($_SESSION['username'])) {
    header('Location: ../login.php');
    exit();
}

// Include database connection
include_once '../db_conn.php';

$errors = [];
$success = false;
$user_id = $_SESSION['user_id'];

// Validasi dan update username
if (isset($_POST['username']) && trim($_POST['username']) !== '') {
    $new_username = htmlspecialchars(trim($_POST['username']));

    // Cek apakah username sudah digunakan oleh user lain
    $stmt = $conn->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
    $stmt->execute([$new_username, $user_id]);

    if ($stmt->rowCount() > 0) {
        $errors[] = 'Username sudah digunakan oleh pengguna lain.';
    } else {
        // Update username di database
        $stmt = $conn->prepare("UPDATE users SET username = ? WHERE id = ?");
        if ($stmt->execute([$new_username, $user_id])) {
            $_SESSION['username'] = $new_username;
        } else {
            $errors[] = 'Gagal mengupdate username.';
        }
    }
} else {
    $errors[] = 'Nama pengguna tidak boleh kosong.';
}

// Update password jika diisi
if (isset($_POST['password']) && trim($_POST['password']) !== '') {
    $new_password = trim($_POST['password']);

    // Validasi panjang password
    if (strlen($new_password) < 6) {
        $errors[] = 'Password minimal 6 karakter.';
    } else {
        // Hash password dan update ke database
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
        $stmt = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
        if (!$stmt->execute([$hashed_password, $user_id])) {
            $errors[] = 'Gagal mengupdate password.';
        }
    }
}

// Upload foto profil jika ada
if (isset($_FILES['foto']) && $_FILES['foto']['error'] === UPLOAD_ERR_OK) {
    $allowed = ['jpg', 'jpeg', 'png', 'gif'];
    $maxSize = 2 * 1024 * 1024; // 2MB
    $fileName = $_FILES['foto']['name'];
    $fileSize = $_FILES['foto']['size'];
    $fileTmpName = $_FILES['foto']['tmp_name'];
    $ext = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

    // Validasi file
    if (!in_array($ext, $allowed)) {
        $errors[] = 'Format foto harus jpg, jpeg, png, atau gif.';
    } elseif ($fileSize > $maxSize) {
        $errors[] = 'Ukuran foto maksimal 2MB.';
    } else {
        // Buat direktori upload jika belum ada
        $uploadDir = '../upload/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }

        // Generate nama file unik
        $newName = 'profile_' . $user_id . '_' . time() . '.' . $ext;
        $dest = $uploadDir . $newName;

        // Hapus foto lama jika ada
        $stmt = $conn->prepare("SELECT profile_pic FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $oldPhoto = $stmt->fetchColumn();

        if (move_uploaded_file($fileTmpName, $dest)) {
            // Update database dengan nama file baru
            $stmt = $conn->prepare("UPDATE users SET profile_pic = ? WHERE id = ?");
            if ($stmt->execute([$newName, $user_id])) {
                $_SESSION['foto'] = 'upload/' . $newName;

                // Hapus foto lama jika ada dan bukan default
                if ($oldPhoto && $oldPhoto !== 'user_default.png' && file_exists($uploadDir . $oldPhoto)) {
                    unlink($uploadDir . $oldPhoto);
                }
            } else {
                $errors[] = 'Gagal menyimpan informasi foto ke database.';
                // Hapus file yang sudah diupload jika gagal simpan ke DB
                if (file_exists($dest)) {
                    unlink($dest);
                }
            }
        } else {
            $errors[] = 'Gagal mengupload foto. Periksa permission folder upload.';
        }
    }
} elseif (isset($_FILES['foto']) && $_FILES['foto']['error'] !== UPLOAD_ERR_NO_FILE) {
    // Handle error upload lainnya
    switch ($_FILES['foto']['error']) {
        case UPLOAD_ERR_INI_SIZE:
        case UPLOAD_ERR_FORM_SIZE:
            $errors[] = 'Ukuran file terlalu besar.';
            break;
        case UPLOAD_ERR_PARTIAL:
            $errors[] = 'File hanya terupload sebagian.';
            break;
        default:
            $errors[] = 'Terjadi kesalahan saat mengupload file.';
            break;
    }
}

// Tentukan status berhasil atau gagal
if (empty($errors)) {
    $success = true;
    $message = 'Profil berhasil diperbarui!';
} else {
    $success = false;
    $message = implode(' ', $errors);
}

// Redirect kembali ke profil dengan pesan
if ($success) {
    header('Location: ../profil.php?success=' . urlencode($message));
    exit();
} else {
    header('Location: ../profil.php?error=' . urlencode($message));
    exit();
}
