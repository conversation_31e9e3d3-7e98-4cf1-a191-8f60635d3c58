<?php
// Include header (sudah ada session check dan DB connection)
include __DIR__ . '/inc/header.php';

// Include data handlers
include_once __DIR__ . '/data/post.php';
include_once __DIR__ . '/data/category.php';

// Helper function
function getCategoryName($categories, $id) {
    foreach ($categories as $cat) {
        if ($cat['id'] == $id) return $cat['category'];
    }
    return '-';
}

// Ambil data dari database
try {
    $posts = function_exists('getAllPosts') ? getAllPosts($conn) : [];
    $categories = function_exists('getAllCategories') ? getAllCategories($conn) : [];
} catch (Exception $e) {
    $posts = [];
    $categories = [];
    $error_message = "Error mengambil data: " . $e->getMessage();
}
?>

<div class="dashboard-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="welcome-header">
            <i class="fa fa-newspaper-o mr-3"></i>Manajemen Postingan
        </h1>
        <a href="post-add.php" class="btn btn-brand-warning">
            <i class="fa fa-plus mr-2"></i>Tambah Postingan
        </a>
    </div>

    <?php if (isset($error_message)): ?>
    <div class="alert alert-danger">
        <i class="fa fa-exclamation-circle mr-2"></i><?= htmlspecialchars($error_message) ?>
    </div>
    <?php endif; ?>

    <?php if (isset($_GET['success'])): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fa fa-check-circle mr-2"></i><?= htmlspecialchars($_GET['success']) ?>
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <?php if (isset($_GET['error'])): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fa fa-exclamation-circle mr-2"></i><?= htmlspecialchars($_GET['error']) ?>
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <div class="card summary-card">
        <div class="card-header card-header-brand-warning">
            <i class="fa fa-newspaper-o mr-2"></i>Daftar Postingan (<?= count($posts) ?> postingan)
        </div>
        <div class="card-body p-0">
            <?php if (count($posts) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead style="background: rgba(255,152,0,0.1);">
                        <tr>
                            <th><i class="fa fa-hashtag mr-1"></i>No</th>
                            <th><i class="fa fa-image mr-1"></i>Cover</th>
                            <th><i class="fa fa-file-text mr-1"></i>Judul</th>
                            <th><i class="fa fa-tag mr-1"></i>Kategori</th>
                            <th><i class="fa fa-calendar mr-1"></i>Tanggal</th>
                            <th><i class="fa fa-eye mr-1"></i>Status</th>
                            <th><i class="fa fa-cogs mr-1"></i>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $no = 1; foreach ($posts as $post): ?>
                        <tr>
                            <td><?= $no++ ?></td>
                            <td>
                                <?php if (!empty($post['cover_url'])): ?>
                                    <img src="../upload/blog/<?= htmlspecialchars($post['cover_url']) ?>"
                                         alt="Cover"
                                         style="width: 60px; height: 40px; border-radius: 8px; object-fit: cover; border: 2px solid #ff9800;">
                                <?php else: ?>
                                    <div style="width: 60px; height: 40px; border-radius: 8px; background: linear-gradient(135deg, #ff9800, #ffb74d); display: flex; align-items: center; justify-content: center; color: #fff; font-size: 12px;">
                                        No Image
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <strong><?= htmlspecialchars($post['post_title'] ?? '-') ?></strong>
                                <br>
                                <small class="text-muted">
                                    <?= htmlspecialchars(substr(strip_tags($post['post_text'] ?? ''), 0, 50)) ?>...
                                </small>
                            </td>
                            <td>
                                <span class="badge badge-warning px-2 py-1">
                                    <?= htmlspecialchars(getCategoryName($categories, $post['category_id'] ?? 0)) ?>
                                </span>
                            </td>
                            <td>
                                <?= $post['created_at'] ? date('d/m/Y', strtotime($post['created_at'])) : '-' ?>
                                <br>
                                <small class="text-muted">
                                    <?= $post['created_at'] ? date('H:i', strtotime($post['created_at'])) : '' ?>
                                </small>
                            </td>
                            <td>
                                <span class="badge badge-success px-2 py-1">
                                    <i class="fa fa-check mr-1"></i>Published
                                </span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="../blog-single.php?id=<?= $post['id'] ?>" class="btn btn-sm btn-info" title="Lihat" target="_blank">
                                        <i class="fa fa-eye"></i>
                                    </a>
                                    <a href="post-edit.php?id=<?= $post['id'] ?>" class="btn btn-sm btn-warning" title="Edit">
                                        <i class="fa fa-edit"></i>
                                    </a>
                                    <a href="post-delete.php?id=<?= $post['id'] ?>" class="btn btn-sm btn-danger"
                                       onclick="return confirm('Apakah Anda yakin ingin menghapus postingan ini?')" title="Hapus">
                                        <i class="fa fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php else: ?>
            <div class="text-center py-5">
                <i class="fa fa-newspaper-o" style="font-size: 4rem; color: #ccc;"></i>
                <h4 class="mt-3 text-muted">Belum ada postingan</h4>
                <p class="text-muted">Klik tombol "Tambah Postingan" untuk membuat postingan baru.</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script src="../js/jquery-1.12.4.min.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script>
$(document).ready(function() {
    // Auto hide alerts
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);

    // Add hover effects
    $('tbody tr').hover(
        function() { $(this).addClass('table-active'); },
        function() { $(this).removeClass('table-active'); }
    );
});
</script>

</body>
</html>
