<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header('Location: ../login.php');
    exit();
}

// Include database connection
include_once __DIR__ . '/../db_conn.php';

// Check if ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: users.php?error=' . urlencode('ID pengguna tidak valid'));
    exit();
}

$user_id = intval($_GET['id']);

// Prevent deleting current logged in user
$current_user_stmt = $conn->prepare("SELECT id FROM users WHERE username = ?");
$current_user_stmt->execute([$_SESSION['username']]);
$current_user = $current_user_stmt->fetch(PDO::FETCH_ASSOC);

if ($current_user && $current_user['id'] == $user_id) {
    header('Location: users.php?error=' . urlencode('Tidak dapat menghapus akun yang sedang digunakan'));
    exit();
}

try {
    // Check if user exists
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
        header('Location: users.php?error=' . urlencode('Pengguna tidak ditemukan'));
        exit();
    }

    // Delete user profile picture if exists
    if (!empty($user['profile_pic']) && file_exists(__DIR__ . '/../upload/' . $user['profile_pic'])) {
        unlink(__DIR__ . '/../upload/' . $user['profile_pic']);
    }

    // Delete user
    $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
    $result = $stmt->execute([$user_id]);

    if ($result) {
        header('Location: users.php?success=' . urlencode('Pengguna berhasil dihapus'));
    } else {
        header('Location: users.php?error=' . urlencode('Gagal menghapus pengguna'));
    }

} catch (Exception $e) {
    header('Location: users.php?error=' . urlencode('Error: ' . $e->getMessage()));
}

exit();
