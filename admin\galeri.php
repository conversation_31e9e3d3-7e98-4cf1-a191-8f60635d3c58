<?php
// Handle delete action before any output
if (isset($_GET['delete'])) {
    include_once __DIR__ . "/data/galeri.php";
    include_once __DIR__ . '/inc/header.php'; // for DB connection/session if needed
    $id = intval($_GET['delete']);
    try {
        $item = getGaleriById($conn, $id);
        if ($item && $item['gambar'] && file_exists(__DIR__ . "/../upload/galeri/".$item['gambar'])) {
            unlink(__DIR__ . "/../upload/galeri/".$item['gambar']);
        }
        deleteGaleri($conn, $id);
        echo "<script>window.location.href='galeri.php?success=" . urlencode("Galeri berhasil dihapus") . "';</script>";
        exit;
    } catch (Exception $e) {
        echo "<script>window.location.href='galeri.php?error=" . urlencode("Error menghapus galeri: " . $e->getMessage()) . "';</script>";
        exit;
    }
}

// Include header (sudah ada session check dan DB connection)
include __DIR__ . '/inc/header.php';

// Include data handler
include_once __DIR__ . "/data/galeri.php";

// Ambil data galeri
try {
    $galeri = getAllGaleri($conn);
} catch (Exception $e) {
    $galeri = [];
    $error_message = "Error mengambil data galeri: " . $e->getMessage();
}
?>
<div class="dashboard-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="welcome-header">
            <i class="fa fa-image mr-3"></i>Manajemen Galeri
        </h1>
        <a href="galeri-add.php" class="btn btn-brand-info">
            <i class="fa fa-plus mr-2"></i>Tambah Galeri
        </a>
    </div>

    <?php if (isset($error_message)): ?>
    <div class="alert alert-danger">
        <i class="fa fa-exclamation-circle mr-2"></i><?= htmlspecialchars($error_message) ?>
    </div>
    <?php endif; ?>

    <?php if (isset($_GET['success'])): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fa fa-check-circle mr-2"></i><?= htmlspecialchars($_GET['success']) ?>
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <?php if (isset($_GET['error'])): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fa fa-exclamation-circle mr-2"></i><?= htmlspecialchars($_GET['error']) ?>
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <div class="card summary-card">
        <div class="card-header card-header-brand-info">
            <i class="fa fa-image mr-2"></i>Daftar Galeri (<?= count($galeri) ?> item)
        </div>
        <div class="card-body p-0">
            <?php if (count($galeri) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead style="background: rgba(23,162,184,0.1);">
                        <tr>
                            <th><i class="fa fa-hashtag mr-1"></i>No</th>
                            <th><i class="fa fa-image mr-1"></i>Media</th>
                            <th><i class="fa fa-file-text mr-1"></i>Judul</th>
                            <th><i class="fa fa-info-circle mr-1"></i>Deskripsi</th>
                            <th><i class="fa fa-tags mr-1"></i>Kategori</th>
                            <th><i class="fa fa-calendar mr-1"></i>Tanggal</th>
                            <th><i class="fa fa-cogs mr-1"></i>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $no = 1; foreach($galeri as $g): ?>
                        <tr>
                            <td><?= $no++ ?></td>
                            <td class="text-center">
                                <?php if(preg_match('/\.(mp4|webm|ogg)$/i', $g['gambar'])): ?>
                                    <video src="../upload/galeri/<?=htmlspecialchars($g['gambar'])?>"
                                           width="80" height="60"
                                           style="border-radius:8px; border: 2px solid #17a2b8; object-fit: cover;"
                                           controls>
                                    </video>
                                    <br><small class="text-info"><i class="fa fa-video-camera mr-1"></i>Video</small>
                                <?php else: ?>
                                    <img src="../upload/galeri/<?=htmlspecialchars($g['gambar'])?>"
                                         alt="<?=htmlspecialchars($g['judul'])?>"
                                         style="width: 80px; height: 60px; border-radius: 8px; object-fit: cover; border: 2px solid #17a2b8;">
                                    <br><small class="text-info"><i class="fa fa-image mr-1"></i>Gambar</small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <strong><?=htmlspecialchars($g['judul'])?></strong>
                            </td>
                            <td>
                                <?= htmlspecialchars(substr($g['deskripsi'], 0, 50)) ?>
                                <?= strlen($g['deskripsi']) > 50 ? '...' : '' ?>
                            </td>
                            <td>
                                <?= htmlspecialchars($g['kategori'] ?? '-') ?>
                            </td>
                            <td>
                                <?= date('d/m/Y', strtotime($g['tanggal'])) ?>
                                <br>
                                <small class="text-muted">
                                    <?= date('H:i', strtotime($g['tanggal'])) ?>
                                </small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="../upload/galeri/<?=htmlspecialchars($g['gambar'])?>"
                                       class="btn btn-sm btn-info" target="_blank" title="Lihat">
                                        <i class="fa fa-eye"></i>
                                    </a>
                                    <a href="galeri-edit.php?id=<?=$g['id']?>"
                                       class="btn btn-sm btn-warning" title="Edit">
                                        <i class="fa fa-edit"></i>
                                    </a>
                                    <a href="galeri.php?delete=<?=$g['id']?>"
                                       class="btn btn-sm btn-danger"
                                       onclick="return confirm('Apakah Anda yakin ingin menghapus item galeri ini?')"
                                       title="Hapus">
                                        <i class="fa fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php else: ?>
            <div class="text-center py-5">
                <i class="fa fa-image" style="font-size: 4rem; color: #ccc;"></i>
                <h4 class="mt-3 text-muted">Belum ada item di galeri</h4>
                <p class="text-muted">Klik tombol "Tambah Galeri" untuk menambah foto atau video.</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script src="../js/jquery-1.12.4.min.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script>
$(document).ready(function() {
    // Auto hide alerts
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);

    // Add hover effects
    $('tbody tr').hover(
        function() { $(this).addClass('table-active'); },
        function() { $(this).removeClass('table-active'); }
    );
});
</script>

</body>
</html>

