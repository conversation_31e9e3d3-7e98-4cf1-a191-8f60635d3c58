/* Dropdown Menus with Cool Animations */
.dropdown-menu-custom {
    background: linear-gradient(135deg, #005a99 0%, #ff9800 100%) !important;
    border: none !important;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1), 0 5px 15px rgba(0,0,0,0.07);
    margin-top: 8px;
    min-width: 220px;
    opacity: 0;
    transform: translateY(-20px) scale(0.95) rotateX(-10deg);
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    visibility: hidden;
    transform-origin: top center;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

/* Create invisible bridge between menu and dropdown for easier navigation */
.nav-item.dropdown::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    height: 8px;
    background: transparent;
    z-index: 1000;
}

/* Cool entrance animation */
.dropdown.show .dropdown-menu-custom,
.nav-item.dropdown.show .dropdown-menu-custom {
    opacity: 1;
    transform: translateY(0) scale(1) rotateX(0deg);
    visibility: visible;
    animation: dropdownSlideIn 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Keyframe animations */
@keyframes dropdownSlideIn {
    0% {
        opacity: 0;
        transform: translateY(-30px) scale(0.8) rotateX(-15deg);
    }
    50% {
        opacity: 0.8;
        transform: translateY(5px) scale(1.02) rotateX(2deg);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1) rotateX(0deg);
    }
}

@keyframes dropdownSlideOut {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1) rotateX(0deg);
    }
    100% {
        opacity: 0;
        transform: translateY(-20px) scale(0.95) rotateX(-10deg);
    }
}

/* Glowing border effect */
.dropdown-menu-custom::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #005a99, #ff9800, #005a99);
    border-radius: 17px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.nav-item.dropdown.show .dropdown-menu-custom::before {
    opacity: 0.3;
    animation: borderGlow 2s ease-in-out infinite alternate;
}

@keyframes borderGlow {
    0% { opacity: 0.3; }
    100% { opacity: 0.6; }
}

/* Enhanced dropdown items with staggered animation */
.dropdown-menu-custom .dropdown-item {
    color: #fff !important;
    padding: 14px 20px;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    border-radius: 10px;
    margin: 6px 10px;
    position: relative;
    overflow: hidden;
    opacity: 0;
    transform: translateX(-20px);
}

/* Staggered animation for dropdown items */
.nav-item.dropdown.show .dropdown-menu-custom .dropdown-item {
    opacity: 1;
    transform: translateX(0);
}

.nav-item.dropdown.show .dropdown-menu-custom .dropdown-item:nth-child(1) {
    animation: slideInItem 0.4s ease 0.1s both;
}

.nav-item.dropdown.show .dropdown-menu-custom .dropdown-item:nth-child(2) {
    animation: slideInItem 0.4s ease 0.2s both;
}

.nav-item.dropdown.show .dropdown-menu-custom .dropdown-item:nth-child(3) {
    animation: slideInItem 0.4s ease 0.3s both;
}

@keyframes slideInItem {
    0% {
        opacity: 0;
        transform: translateX(-30px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Enhanced hover effects */
.dropdown-menu-custom .dropdown-item:hover {
    background: rgba(255,255,255,0.25) !important;
    transform: translateX(8px) scale(1.02);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Ripple effect on click */
.dropdown-menu-custom .dropdown-item::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255,255,255,0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.dropdown-menu-custom .dropdown-item:active::after {
    width: 300px;
    height: 300px;
}

.dropdown-menu-custom .dropdown-item i {
    width: 20px;
    text-align: center;
    transition: transform 0.3s ease;
}

.dropdown-menu-custom .dropdown-item:hover i {
    transform: scale(1.2) rotate(5deg);
}

/* Floating animation for dropdown toggle */
.nav-item.dropdown .dropdown-toggle {
    transition: all 0.3s ease;
}

.nav-item.dropdown:hover .dropdown-toggle {
    transform: translateY(-2px);
}

/* Pulse animation for active dropdown */
.nav-item.dropdown.show .dropdown-toggle {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 152, 0, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 152, 0, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 152, 0, 0);
    }
}

/* Mobile specific animations */
@media (max-width: 991px) {
    .dropdown-menu-custom {
        position: static !important;
        transform: none !important;
        opacity: 1 !important;
        visibility: visible !important;
        background: rgba(255,255,255,0.1) !important;
        box-shadow: none !important;
        margin: 8px 0 0 0 !important;
        border-radius: 8px;
        animation: none !important;
    }
    
    .dropdown-menu-custom .dropdown-item {
        opacity: 1 !important;
        transform: none !important;
        animation: none !important;
        padding: 10px 20px;
        margin: 2px 0;
        font-size: 15px;
    }
    
    .dropdown-menu-custom::before {
        display: none;
    }
}
