<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header('Location: ../login.php');
    exit();
}

// Include database connection
include_once __DIR__ . '/../db_conn.php';
include_once __DIR__ . '/data/galeri.php';

// Check if ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: galeri.php?error=' . urlencode('ID galeri tidak valid'));
    exit();
}

$id = intval($_GET['id']);
$errors = [];
$success = '';

// Get galeri data
$data = getGaleriById($conn, $id);
if (!$data) {
    header('Location: galeri.php?error=' . urlencode('Data galeri tidak ditemukan'));
    exit();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $judul = trim($_POST['judul'] ?? '');
    $deskripsi = trim($_POST['deskripsi'] ?? '');

    // Validation
    if (empty($judul)) {
        $errors[] = 'Judul tidak boleh kosong';
    }

    if (empty($deskripsi)) {
        $errors[] = 'Deskripsi tidak boleh kosong';
    }

    // Handle file upload
    $gambar = $data['gambar']; // Keep existing file by default

    if (!empty($_FILES['gambar']['name'])) {
        $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'mp4', 'webm', 'ogg'];
        $file_info = pathinfo($_FILES['gambar']['name']);
        $ext = strtolower($file_info['extension']);

        if (!in_array($ext, $allowed_types)) {
            $errors[] = 'Format file tidak didukung. Gunakan: ' . implode(', ', $allowed_types);
        } else {
            $namaFile = time() . '_' . rand(100, 999) . '.' . $ext;
            $tujuan = __DIR__ . '/../upload/galeri/' . $namaFile;

            // Create directory if not exists
            $upload_dir = __DIR__ . '/../upload/galeri/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            if (move_uploaded_file($_FILES['gambar']['tmp_name'], $tujuan)) {
                // Delete old file if exists
                if ($gambar && file_exists(__DIR__ . '/../upload/galeri/' . $gambar)) {
                    unlink(__DIR__ . '/../upload/galeri/' . $gambar);
                }
                $gambar = $namaFile;
            } else {
                $errors[] = 'Gagal mengupload file';
            }
        }
    }

    // Update to database if no errors
    if (empty($errors)) {
        try {
            if (updateGaleri($conn, $id, $judul, $gambar, $deskripsi)) {
                header('Location: galeri.php?success=' . urlencode('Galeri berhasil diupdate'));
                exit();
            } else {
                $errors[] = 'Gagal mengupdate galeri ke database';
            }
        } catch (Exception $e) {
            $errors[] = 'Error: ' . $e->getMessage();
        }
    }
}

// Include header after all redirect logic
include __DIR__ . '/inc/header.php';
?>

<div class="dashboard-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="welcome-header">
            <i class="fa fa-edit mr-3"></i>Edit Galeri
        </h1>
        <a href="galeri.php" class="btn btn-secondary">
            <i class="fa fa-arrow-left mr-2"></i>Kembali ke Galeri
        </a>
    </div>

    <?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <i class="fa fa-exclamation-circle mr-2"></i>
        <strong>Terjadi kesalahan:</strong>
        <ul class="mb-0 mt-2">
            <?php foreach ($errors as $error): ?>
            <li><?= htmlspecialchars($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php endif; ?>

    <?php if (!empty($success)): ?>
    <div class="alert alert-success">
        <i class="fa fa-check-circle mr-2"></i>
        <?= htmlspecialchars($success) ?>
    </div>
    <?php endif; ?>

    <div class="card summary-card">
        <div class="card-header card-header-brand-warning">
            <i class="fa fa-edit mr-2"></i>Form Edit Galeri
        </div>
        <div class="card-body">
            <form method="POST" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group">
                            <label for="judul"><i class="fa fa-heading mr-1"></i>Judul Galeri *</label>
                            <input type="text" class="form-control" id="judul" name="judul"
                                   value="<?= htmlspecialchars($data['judul']) ?>"
                                   placeholder="Masukkan judul galeri" required>
                        </div>

                        <div class="form-group">
                            <label for="deskripsi"><i class="fa fa-file-text mr-1"></i>Deskripsi *</label>
                            <textarea class="form-control" id="deskripsi" name="deskripsi" rows="4"
                                      placeholder="Masukkan deskripsi galeri" required><?= htmlspecialchars($data['deskripsi']) ?></textarea>
                        </div>

                        <div class="form-group">
                            <label for="gambar"><i class="fa fa-upload mr-1"></i>Upload Media Baru</label>
                            <input type="file" class="form-control-file" id="gambar" name="gambar"
                                   accept="image/*,video/*">
                            <small class="form-text text-muted">
                                Format yang didukung: JPG, JPEG, PNG, GIF, MP4, WEBM, OGG. Kosongkan jika tidak ingin mengganti media.
                            </small>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <i class="fa fa-image mr-2"></i>Media Saat Ini
                            </div>
                            <div class="card-body text-center">
                                <?php if (preg_match('/\.(mp4|webm|ogg)$/i', $data['gambar'])): ?>
                                    <video src="../upload/galeri/<?= htmlspecialchars($data['gambar']) ?>"
                                           class="img-fluid rounded shadow-sm mb-2"
                                           style="max-width: 100%; max-height: 200px;" controls>
                                        Browser Anda tidak mendukung video.
                                    </video>
                                    <br>
                                    <span class="badge badge-info">
                                        <i class="fa fa-video-camera mr-1"></i>Video
                                    </span>
                                <?php else: ?>
                                    <img src="../upload/galeri/<?= htmlspecialchars($data['gambar']) ?>"
                                         class="img-fluid rounded shadow-sm mb-2"
                                         style="max-width: 100%; max-height: 200px;"
                                         alt="<?= htmlspecialchars($data['judul']) ?>">
                                    <br>
                                    <span class="badge badge-success">
                                        <i class="fa fa-image mr-1"></i>Gambar
                                    </span>
                                <?php endif; ?>

                                <div class="mt-2">
                                    <small class="text-muted">
                                        <strong>File:</strong> <?= htmlspecialchars($data['gambar']) ?>
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-3">
                            <div class="card-header">
                                <i class="fa fa-info-circle mr-2"></i>Informasi
                            </div>
                            <div class="card-body">
                                <small class="text-muted">
                                    <p><strong>ID:</strong> <?= $data['id'] ?></p>
                                    <p><strong>Dibuat:</strong> <?= date('d/m/Y H:i', strtotime($data['created_at'] ?? 'now')) ?></p>
                                    <p class="mb-0"><strong>Status:</strong>
                                        <span class="badge badge-success">Aktif</span>
                                    </p>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <hr>

                <div class="form-group mb-0">
                    <button type="submit" class="btn btn-brand-warning">
                        <i class="fa fa-save mr-2"></i>Update Galeri
                    </button>
                    <a href="galeri.php" class="btn btn-secondary ml-2">
                        <i class="fa fa-times mr-2"></i>Batal
                    </a>
                    <button type="reset" class="btn btn-outline-secondary ml-2">
                        <i class="fa fa-refresh mr-2"></i>Reset Form
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="../js/jquery-1.12.4.min.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script>
$(document).ready(function() {
    // Auto hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);

    // File input change handler
    $('#gambar').change(function() {
        var file = this.files[0];
        if (file) {
            var fileSize = file.size / 1024 / 1024; // MB
            if (fileSize > 10) {
                alert('Ukuran file terlalu besar! Maksimal 10MB.');
                $(this).val('');
                return;
            }

            var fileName = file.name;
            var fileExt = fileName.split('.').pop().toLowerCase();
            var allowedExts = ['jpg', 'jpeg', 'png', 'gif', 'mp4', 'webm', 'ogg'];

            if (allowedExts.indexOf(fileExt) === -1) {
                alert('Format file tidak didukung! Gunakan: ' + allowedExts.join(', '));
                $(this).val('');
                return;
            }
        }
    });

    // Form validation
    $('form').submit(function(e) {
        var judul = $('#judul').val().trim();
        var deskripsi = $('#deskripsi').val().trim();

        if (judul === '') {
            alert('Judul tidak boleh kosong!');
            $('#judul').focus();
            e.preventDefault();
            return false;
        }

        if (deskripsi === '') {
            alert('Deskripsi tidak boleh kosong!');
            $('#deskripsi').focus();
            e.preventDefault();
            return false;
        }

        return true;
    });
});
</script>

</body>
</html>
