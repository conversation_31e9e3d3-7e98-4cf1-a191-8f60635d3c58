<!DOCTYPE html>
<html lang="id">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>Daftar Akun - UKM Panahan Gendewa Geni</title>
	<meta name="description" content="Daftar akun baru di website resmi UKM Panahan Universitas Semarang Gendewa Geni">
	
	<!-- Bootstrap CSS -->
	<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-1BmE4kWBq78iYhFldvKuhfTAU6auU8tT94WrHftjDbrCEXSU1oBoqyl2QvZ6jIW3" crossorigin="anonymous">
	
	<!-- FontAwesome -->
	<link rel="stylesheet" href="css/font-awesome.min.css">
	
	<!-- Custom Login CSS (reuse for consistency) -->
	<link rel="stylesheet" href="css/login-custom.css">
	
	<!-- Google Fonts -->
	<link rel="preconnect" href="https://fonts.googleapis.com">
	<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
	<link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Teko:wght@300..700&display=swap" rel="stylesheet">
</head>
<body class="login-page">
	<?php include 'inc/navbar.php'; ?>

    <div class="login-container">
    	<div class="login-card">
    		<!-- Signup Header -->
    		<div class="login-header">
    			<h1><i class="fa fa-user-plus mr-2"></i>DAFTAR</h1>
    			<p>Buat akun baru UKM Panahan Gendewa Geni</p>
    		</div>

    		<!-- Alert Messages -->
    		<?php if(isset($_GET['error'])): ?>
    		<div class="alert alert-danger" role="alert">
    			<i class="fa fa-exclamation-circle mr-2"></i>
			  	<?php echo htmlspecialchars($_GET['error']); ?>
			</div>
		    <?php endif; ?>

		    <?php if(isset($_GET['success'])): ?>
    		<div class="alert alert-success" role="alert">
    			<i class="fa fa-check-circle mr-2"></i>
			  	<?php echo htmlspecialchars($_GET['success']); ?>
			</div>
		    <?php endif; ?>

    		<!-- Signup Form -->
    		<form action="php/signup.php" method="post" id="signupForm" enctype="multipart/form-data">
		  		<div class="form-group">
		    		<label class="form-label">
		    			<i class="fa fa-user" style="color: #005a99;"></i>
		    			Nama Lengkap
		    		</label>
		    		<input type="text" 
		           	class="form-control modern-input"
		           	name="fname"
		           	id="fullname"
		           	placeholder="Masukkan nama lengkap Anda"
		           	value="<?php echo isset($_GET['fname']) ? htmlspecialchars($_GET['fname']) : '' ?>"
		           	required>
		  		</div>

		  		<div class="form-group">
		    		<label class="form-label">
		    			<i class="fa fa-at" style="color: #ff9800;"></i>
		    			Username
		    		</label>
		    		<input type="text" 
		           	class="form-control modern-input"
		           	name="uname"
		           	id="username"
		           	placeholder="Masukkan username Anda"
		           	value="<?php echo isset($_GET['uname']) ? htmlspecialchars($_GET['uname']) : '' ?>"
		           	required>
		  		</div>

		  		<div class="form-group">
		    		<label class="form-label">
		    			<i class="fa fa-lock" style="color: #111;"></i>
		    			Password
		    		</label>
		    		<div style="position: relative;">
			    		<input type="password" 
			           	class="form-control modern-input"
			           	name="pass"
			           	id="password"
			           	placeholder="Masukkan password Anda"
			           	required>
			        	<span class="password-toggle" onclick="togglePassword()">
			        		<i class="fa fa-eye" id="toggleIcon"></i>
			        	</span>
		        	</div>
		        	<small class="text-muted">Password minimal 6 karakter</small>
		  		</div>

		  		<div class="form-group">
		    		<label class="form-label">
		    			<i class="fa fa-camera" style="color: #005a99;"></i>
		    			Foto Profil <span class="text-muted">(Opsional)</span>
		    		</label>
		    		<input type="file"
		           	class="form-control modern-input"
		           	name="profile_pic"
		           	id="profilePic"
		           	accept="image/*"
		           	onchange="previewProfileImage(this)">
		           	<small class="text-muted">Format: JPG, PNG, GIF. Maksimal 2MB</small>
		           	<div id="profileImagePreview" class="mt-3" style="display: none;">
		                <img id="profilePreview" src="" alt="Preview" style="max-width: 150px; max-height: 150px; border-radius: 50%; box-shadow: 0 4px 15px rgba(0,90,153,0.3); object-fit: cover;">
		            </div>
		  		</div>

		  		<button type="submit" class="btn-login" id="signupBtn">
		  			<i class="fa fa-user-plus mr-2"></i>
		  			Daftar Sekarang
		  		</button>

		  		<div class="signup-link">
		  			<p>Sudah punya akun? <a href="login.php">Masuk di sini</a></p>
		  		</div>
			</form>
    	</div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p" crossorigin="anonymous"></script>

    <script>
    // Toggle password visibility
    function togglePassword() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.getElementById('toggleIcon');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    }

    // Preview profile image before upload
    function previewProfileImage(input) {
        const preview = document.getElementById('profilePreview');
        const previewContainer = document.getElementById('profileImagePreview');

        if (input.files && input.files[0]) {
            const file = input.files[0];

            // Validasi ukuran file (2MB)
            if (file.size > 2 * 1024 * 1024) {
                alert('Ukuran file terlalu besar! Maksimal 2MB.');
                input.value = '';
                previewContainer.style.display = 'none';
                return;
            }

            // Validasi tipe file
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            if (!allowedTypes.includes(file.type)) {
                alert('Format file tidak didukung! Gunakan JPG, PNG, atau GIF.');
                input.value = '';
                previewContainer.style.display = 'none';
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                preview.src = e.target.result;
                previewContainer.style.display = 'block';
                previewContainer.style.textAlign = 'center';
            };
            reader.readAsDataURL(file);
        } else {
            previewContainer.style.display = 'none';
        }
    }

    // Form validation and loading state
    document.getElementById('signupForm').addEventListener('submit', function(e) {
        const fullname = document.getElementById('fullname').value.trim();
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        const profilePic = document.getElementById('profilePic').files[0];
        const signupBtn = document.getElementById('signupBtn');

        // Basic validation
        if (fullname.length < 2) {
            alert('Nama lengkap minimal 2 karakter!');
            e.preventDefault();
            return;
        }

        if (username.length < 3) {
            alert('Username minimal 3 karakter!');
            e.preventDefault();
            return;
        }

        if (password.length < 6) {
            alert('Password minimal 6 karakter!');
            e.preventDefault();
            return;
        }

        // Validasi foto profil jika ada
        if (profilePic) {
            if (profilePic.size > 2 * 1024 * 1024) {
                alert('Ukuran foto terlalu besar! Maksimal 2MB.');
                e.preventDefault();
                return;
            }

            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            if (!allowedTypes.includes(profilePic.type)) {
                alert('Format foto tidak didukung! Gunakan JPG, PNG, atau GIF.');
                e.preventDefault();
                return;
            }
        }

        // Konfirmasi submit
        if (!confirm('Apakah data yang Anda masukkan sudah benar?')) {
            e.preventDefault();
            return;
        }

        // Add loading state
        signupBtn.classList.add('loading');
        signupBtn.innerHTML = '<i class="fa fa-spinner fa-spin mr-2"></i>Memproses...';
        signupBtn.disabled = true;
    });

    // Auto hide alerts after 5 seconds
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            if (alert.classList.contains('fade')) {
                alert.classList.remove('show');
            } else {
                alert.style.opacity = '0';
                alert.style.transform = 'translateY(-20px)';
                setTimeout(() => alert.remove(), 300);
            }
        });
    }, 5000);

    // Add focus effects
    document.querySelectorAll('.modern-input').forEach(function(input) {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
    </script>
</body>
</html>
