-- Create jabatan table for managing positions/roles
CREATE TABLE IF NOT EXISTS `jabatan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nama_jabatan` varchar(50) NOT NULL,
  `deskripsi` text DEFAULT NULL,
  `urutan` int(3) DEFAULT 1,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `nama_jabatan` (`nama_jabatan`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default jabatan data
INSERT INTO `jabatan` (`nama_jabatan`, `deskripsi`, `urutan`, `is_active`) VALUES
('<PERSON><PERSON><PERSON>', 'Ketua UKM Panahan Gendewa Geni', 1, 1),
('<PERSON>aki<PERSON> Ketua', 'Wakil Ketua UKM Panahan Gendewa Geni', 2, 1),
('Sekretaris', 'Sekretaris UKM Panahan Gendewa Geni', 3, 1),
('Bendahara', 'Bendahara UKM Panahan Gendewa Geni', 4, 1),
('Koordinator Divisi Latihan', 'Koordinator untuk divisi latihan dan pelatihan', 5, 1),
('Koordinator Divisi Event', 'Koordinator untuk divisi acara dan event', 6, 1),
('Koordinator Divisi Humas', 'Koordinator untuk divisi hubungan masyarakat', 7, 1),
('Anggota', 'Anggota UKM Panahan Gendewa Geni', 8, 1);
