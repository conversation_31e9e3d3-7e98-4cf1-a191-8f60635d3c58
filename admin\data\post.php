<?php
include_once __DIR__ . '/../../db_conn.php';

function getAllPosts($conn) {
    // Query tanpa ORDER BY, agar tidak error jika tidak tahu nama kolom
    $stmt = $conn->query("SELECT * FROM post");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getPostById($conn, $id) {
    $stmt = $conn->prepare("SELECT * FROM post WHERE id = ? LIMIT 1");
    $stmt->execute([$id]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function getById($conn, $id) {
    return getPostById($conn, $id);
}

function searchPosts($conn, $key) {
    $stmt = $conn->prepare("SELECT * FROM post WHERE post_title LIKE ? OR post_text LIKE ? ORDER BY created_at DESC");
    $like = "%$key%";
    $stmt->execute([$like, $like]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getLatestPosts($conn, $limit = 6) {
    $stmt = $conn->prepare("SELECT * FROM post ORDER BY created_at DESC LIMIT ?");
    $stmt->bindValue(1, (int)$limit, PDO::PARAM_INT);
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}
