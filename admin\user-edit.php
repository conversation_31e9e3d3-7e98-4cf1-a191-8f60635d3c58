<?php
// Check if ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: users.php?error=' . urlencode('ID pengguna tidak valid'));
    exit();
}

$id = intval($_GET['id']);
$errors = [];
$user = null;

// Get user data
try {
    include __DIR__ . '/inc/header.php'; // for DB connection/session if needed
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
        header('Location: users.php?error=' . urlencode('Pengguna tidak ditemukan'));
        exit();
    }
} catch (Exception $e) {
    header('Location: users.php?error=' . urlencode('Error: ' . $e->getMessage()));
    exit();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = trim($_POST['username'] ?? '');
    $fname = trim($_POST['fname'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $role = $_POST['role'] ?? 'anggota';
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';

    // Validation
    if (empty($username)) {
        $errors[] = 'Username tidak boleh kosong';
    } elseif (strlen($username) < 3) {
        $errors[] = 'Username minimal 3 karakter';
    }

    if (empty($fname)) {
        $errors[] = 'Nama lengkap tidak boleh kosong';
    }

    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Email tidak valid';
    }

    // Check password if provided
    if (!empty($password)) {
        if (strlen($password) < 6) {
            $errors[] = 'Password minimal 6 karakter';
        }

        if ($password !== $confirm_password) {
            $errors[] = 'Konfirmasi password tidak cocok';
        }
    }

    // Check if username or email already exists (excluding current user)
    if (empty($errors)) {
        try {
            $stmt = $conn->prepare("SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?");
            $stmt->execute([$username, $email, $id]);
            if ($stmt->fetch()) {
                $errors[] = 'Username atau email sudah digunakan oleh pengguna lain';
            }
        } catch (Exception $e) {
            $errors[] = 'Error checking existing user: ' . $e->getMessage();
        }
    }

    // Update user
    if (empty($errors)) {
        try {
            if (!empty($password)) {
                // Update with new password
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                $stmt = $conn->prepare("UPDATE users SET username = ?, fname = ?, email = ?, password = ?, role = ?, updated_at = NOW() WHERE id = ?");
                $result = $stmt->execute([$username, $fname, $email, $hashed_password, $role, $id]);
            } else {
                // Update without changing password
                $stmt = $conn->prepare("UPDATE users SET username = ?, fname = ?, email = ?, role = ?, updated_at = NOW() WHERE id = ?");
                $result = $stmt->execute([$username, $fname, $email, $role, $id]);
            }

            if ($result) {
                echo "<script>window.location.href='users.php?success=" . urlencode('Pengguna berhasil diupdate') . "';</script>";
                exit();
            } else {
                $errors[] = 'Gagal mengupdate pengguna';
            }
        } catch (Exception $e) {
            $errors[] = 'Error: ' . $e->getMessage();
        }
    }
}
?>

<div class="dashboard-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="welcome-header">
            <i class="fa fa-edit mr-3"></i>Edit Pengguna
        </h1>
        <a href="users.php" class="btn btn-secondary">
            <i class="fa fa-arrow-left mr-2"></i>Kembali
        </a>
    </div>

    <?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <i class="fa fa-exclamation-circle mr-2"></i>
        <ul class="mb-0">
            <?php foreach ($errors as $error): ?>
            <li><?= htmlspecialchars($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php endif; ?>

    <div class="card summary-card">
        <div class="card-header card-header-brand-success">
            <i class="fa fa-edit mr-2"></i>Form Edit Pengguna
        </div>
        <div class="card-body">
            <form method="POST">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="username"><i class="fa fa-user mr-1"></i>Username *</label>
                            <input type="text" class="form-control" id="username" name="username"
                                   value="<?= htmlspecialchars($user['username'] ?? '') ?>"
                                   placeholder="Masukkan username" required>
                            <small class="form-text text-muted">Minimal 3 karakter, hanya huruf, angka, dan underscore</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="fname"><i class="fa fa-id-card mr-1"></i>Nama Lengkap *</label>
                            <input type="text" class="form-control" id="fname" name="fname"
                                   value="<?= htmlspecialchars($user['fname'] ?? '') ?>"
                                   placeholder="Masukkan nama lengkap" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email"><i class="fa fa-envelope mr-1"></i>Email *</label>
                            <input type="email" class="form-control" id="email" name="email"
                                   value="<?= htmlspecialchars($user['email'] ?? '') ?>"
                                   placeholder="Masukkan email" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="role"><i class="fa fa-shield mr-1"></i>Role</label>
                            <select class="form-control" id="role" name="role">
                                <option value="anggota" <?= ($user['role'] ?? '') == 'anggota' ? 'selected' : '' ?>>Anggota</option>
                                <option value="admin" <?= ($user['role'] ?? '') == 'admin' ? 'selected' : '' ?>>Admin</option>
                            </select>
                        </div>
                    </div>
                </div>

                <hr>
                <h5><i class="fa fa-lock mr-2"></i>Ubah Password (Opsional)</h5>
                <p class="text-muted">Kosongkan jika tidak ingin mengubah password</p>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="password"><i class="fa fa-lock mr-1"></i>Password Baru</label>
                            <input type="password" class="form-control" id="password" name="password"
                                   placeholder="Masukkan password baru">
                            <small class="form-text text-muted">Minimal 6 karakter</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="confirm_password"><i class="fa fa-lock mr-1"></i>Konfirmasi Password</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password"
                                   placeholder="Ulangi password baru">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label><i class="fa fa-info-circle mr-1"></i>Informasi Akun</label>
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <strong>ID Pengguna:</strong> <?= htmlspecialchars($user['id']) ?><br>
                                <strong>Terdaftar:</strong> <?= isset($user['created_at']) ? date('d/m/Y H:i', strtotime($user['created_at'])) : '-' ?>
                            </small>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">
                                <strong>Terakhir Update:</strong> <?= isset($user['updated_at']) ? date('d/m/Y H:i', strtotime($user['updated_at'])) : '-' ?><br>
                                <strong>Status:</strong> <span class="text-success">Aktif</span>
                            </small>
                        </div>
                    </div>
                </div>

                <div class="form-group text-right">
                    <a href="users.php" class="btn btn-secondary mr-2">
                        <i class="fa fa-times mr-1"></i>Batal
                    </a>
                    <button type="submit" class="btn btn-brand-success">
                        <i class="fa fa-save mr-1"></i>Update Pengguna
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="../js/jquery-1.12.4.min.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script>
$(document).ready(function() {
    // Username validation
    $('#username').on('input', function() {
        var username = $(this).val();
        var regex = /^[a-zA-Z0-9_]+$/;

        if (username.length > 0 && !regex.test(username)) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">Username hanya boleh mengandung huruf, angka, dan underscore</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    // Password confirmation
    $('#confirm_password').on('input', function() {
        var password = $('#password').val();
        var confirm = $(this).val();

        if (confirm.length > 0 && password !== confirm) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">Password tidak cocok</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    // Form validation
    $('form').on('submit', function(e) {
        var isValid = true;

        // Check required fields
        $(this).find('input[required]').each(function() {
            if (!$(this).val().trim()) {
                $(this).addClass('is-invalid');
                isValid = false;
            } else {
                $(this).removeClass('is-invalid');
            }
        });

        // Check password confirmation if password is provided
        var password = $('#password').val();
        var confirm = $('#confirm_password').val();

        if (password && password !== confirm) {
            $('#confirm_password').addClass('is-invalid');
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
            alert('Mohon lengkapi semua field yang wajib diisi!');
        }
    });
});
</script>

</body>
</html>
