<?php
// Include header (sudah ada session check dan DB connection)
include __DIR__ . '/inc/header.php';

// Include data handler
include_once __DIR__ . '/data/category.php';

$errors = [];
$success = false;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $category = trim($_POST['category'] ?? '');

    // Validation
    if (empty($category)) {
        $errors[] = 'Nama kategori tidak boleh kosong';
    } elseif (strlen($category) < 3) {
        $errors[] = 'Nama kategori minimal 3 karakter';
    } elseif (strlen($category) > 50) {
        $errors[] = 'Nama kategori maksimal 50 karakter';
    }

    // Check if category already exists
    if (empty($errors)) {
        try {
            $stmt = $conn->prepare("SELECT id FROM category WHERE category = ?");
            $stmt->execute([$category]);
            if ($stmt->fetch()) {
                $errors[] = 'Kategori dengan nama tersebut sudah ada';
            }
        } catch (Exception $e) {
            $errors[] = 'Error checking existing category: ' . $e->getMessage();
        }
    }

    // Insert to database
    if (empty($errors)) {
        try {
            $stmt = $conn->prepare("INSERT INTO category (category) VALUES (?)");
            $result = $stmt->execute([$category]);
            if ($result) {
                echo '<script>window.location.href = "category.php?success=' . urlencode('Kategori berhasil ditambahkan') . '";</script>';
                exit();
            } else {
                $errors[] = 'Gagal menambahkan kategori ke database';
            }
        } catch (Exception $e) {
            $errors[] = 'Error: ' . $e->getMessage();
        }
    }
}
?>

<div class="dashboard-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="welcome-header">
            <i class="fa fa-plus mr-3"></i>Tambah Kategori Baru
        </h1>
        <a href="category.php" class="btn btn-secondary">
            <i class="fa fa-arrow-left mr-2"></i>Kembali
        </a>
    </div>

    <?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <i class="fa fa-exclamation-circle mr-2"></i>
        <ul class="mb-0">
            <?php foreach ($errors as $error): ?>
            <li><?= htmlspecialchars($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php endif; ?>

    <div class="card summary-card">
        <div class="card-header card-header-brand-info">
            <i class="fa fa-plus mr-2"></i>Form Tambah Kategori
        </div>
        <div class="card-body">
            <form method="POST">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="category"><i class="fa fa-tag mr-1"></i>Nama Kategori *</label>
                            <input type="text" class="form-control" id="category" name="category"
                                   value="<?= htmlspecialchars($_POST['category'] ?? '') ?>"
                                   placeholder="Masukkan nama kategori" required>
                            <small class="form-text text-muted">3-50 karakter, harus unik</small>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label><i class="fa fa-eye mr-1"></i>Preview</label>
                    <div class="preview-container p-3" style="background: #f8f9fa; border-radius: 8px; border: 1px solid #dee2e6;">
                        <span class="badge px-3 py-2" id="categoryPreview" style="background-color: #17a2b8; color: #fff; font-size: 1rem;">
                            <i class="fa fa-tag mr-1"></i>
                            <span id="previewText">Nama Kategori</span>
                        </span>
                    </div>
                </div>

                <div class="form-group text-right">
                    <a href="category.php" class="btn btn-secondary mr-2">
                        <i class="fa fa-times mr-1"></i>Batal
                    </a>
                    <button type="submit" class="btn btn-brand-info">
                        <i class="fa fa-save mr-1"></i>Simpan Kategori
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="../js/jquery-1.12.4.min.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script>
$(document).ready(function() {
    // Live preview
    function updatePreview() {
        var name = $('#category').val() || 'Nama Kategori';

        $('#previewText').text(name);
    }

    // Update preview on input
    $('#category').on('input', updatePreview);

    // Initial preview
    updatePreview();

    // Category name validation
    $('#category').on('input', function() {
        var value = $(this).val();
        var length = value.length;

        if (length < 3 && length > 0) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">Nama kategori minimal 3 karakter</div>');
            }
        } else if (length > 50) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">Nama kategori maksimal 50 karakter</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    // Form validation
    $('form').on('submit', function(e) {
        var isValid = true;
        var category = $('#category').val().trim();

        if (!category || category.length < 3 || category.length > 50) {
            $('#category').addClass('is-invalid');
            isValid = false;
        } else {
            $('#category').removeClass('is-invalid');
        }

        if (!isValid) {
            e.preventDefault();
            alert('Mohon periksa kembali nama kategori!');
        }
    });
});
</script>

</body>
</html>
