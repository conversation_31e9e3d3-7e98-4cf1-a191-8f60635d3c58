<?php
// <PERSON><PERSON> session dan cek login
session_start();
if (!isset($_SESSION['username'])) {
    header('Location: login.php');
    exit();
}

// Ambil data profil dari database
include_once 'db_conn.php';
$user_id = $_SESSION['user_id'];

$stmt = $conn->prepare("SELECT username, fname, profile_pic, role FROM users WHERE id = ?");
$stmt->execute([$user_id]);
$user_data = $stmt->fetch(PDO::FETCH_ASSOC);

if ($user_data) {
    $username = htmlspecialchars($user_data['username']);
    $email = isset($_SESSION['email']) ? htmlspecialchars($_SESSION['email']) : '<EMAIL>';
    $role = $user_data['role'] ? htmlspecialchars($user_data['role']) : 'Anggota';
    $foto = $user_data['profile_pic'] ? 'upload/' . $user_data['profile_pic'] : 'images/all-icon/user.png';

    // Update session dengan data terbaru
    $_SESSION['username'] = $username;
    $_SESSION['foto'] = $foto;
} else {
    // Fallback jika data tidak ditemukan
    $username = htmlspecialchars($_SESSION['username']);
    $email = '<EMAIL>';
    $role = 'Anggota';
    $foto = 'images/all-icon/user.png';
}

// Include header/navbar
include 'inc/navbar.php';
?>

<!-- Custom CSS untuk halaman profil -->
<link rel="stylesheet" href="css/profile-custom.css">

<style>
/* Responsive header/navbar */
@media (max-width: 991px) {
    .navbar-nav { flex-direction: column !important; align-items: flex-start !important; }
    .navbar-nav .nav-item { margin-left: 0 !important; margin-bottom: 8px; }
    .navbar-brand img { max-width: 120px; height: auto; }
    footer, .footer { text-align: center !important; padding: 18px 6px !important; font-size: 0.98rem; }
}
</style>

<body class="profile-page">

<!-- Notifikasi Success/Error -->
<?php if (isset($_GET['success'])): ?>
<div class="container mt-3">
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fa fa-check-circle mr-2"></i><?php echo htmlspecialchars($_GET['success']); ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
</div>
<?php endif; ?>

<?php if (isset($_GET['error'])): ?>
<div class="container mt-3">
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fa fa-exclamation-circle mr-2"></i><?php echo htmlspecialchars($_GET['error']); ?>
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
</div>
<?php endif; ?>

<div class="container mt-5 mb-5">
    <div class="row justify-content-center">
        <div class="col-md-10 col-lg-8">
            <div class="card shadow-lg border-0 rounded-lg profile-container">
                <div class="card-header profile-header text-center py-4">
                    <h3 class="mb-0"><i class="fa fa-user-circle mr-2"></i>Profil Pengguna</h3>
                </div>
                <div class="card-body p-5">
                    <div class="row align-items-center">
                        <div class="col-md-4 text-center mb-4 mb-md-0">
                            <div class="profile-avatar">
                                <img src="<?php echo $foto; ?>" alt="User" class="img-fluid rounded-circle" style="width: 140px; height: 140px; object-fit: cover;">
                            </div>
                            <div class="mt-3">
                                <h5 class="text-primary font-weight-bold"><?php echo $username; ?></h5>
                                <span class="badge badge-gradient px-3 py-2" style="background: linear-gradient(45deg, #005a99, #ff9800); color: #fff; font-size: 12px; border-radius: 15px;"><?php echo $role; ?></span>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="profile-info-card p-4" style="background: linear-gradient(135deg, rgba(0,90,153,0.05) 0%, rgba(255,152,0,0.05) 100%); border-radius: 15px; border: 1px solid rgba(0,90,153,0.1);">
                                <h5 class="mb-3" style="color: #005a99; font-weight: 600;"><i class="fa fa-info-circle mr-2"></i>Informasi Akun</h5>
                                <div class="info-item mb-3">
                                    <div class="row">
                                        <div class="col-4">
                                            <strong style="color: #333;"><i class="fa fa-user mr-2" style="color: #005a99;"></i>Username</strong>
                                        </div>
                                        <div class="col-8">
                                            <span style="color: #666;"><?php echo $username; ?></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="info-item mb-3">
                                    <div class="row">
                                        <div class="col-4">
                                            <strong style="color: #333;"><i class="fa fa-envelope mr-2" style="color: #ff9800;"></i>Email</strong>
                                        </div>
                                        <div class="col-8">
                                            <span style="color: #666;"><?php echo $email; ?></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <div class="row">
                                        <div class="col-4">
                                            <strong style="color: #333;"><i class="fa fa-shield mr-2" style="color: #111;"></i>Role</strong>
                                        </div>
                                        <div class="col-8">
                                            <span style="color: #666;"><?php echo $role; ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4">
                                <div class="edit-form-card p-4" style="background: linear-gradient(135deg, rgba(255,152,0,0.05) 0%, rgba(17,17,17,0.05) 100%); border-radius: 15px; border: 1px solid rgba(255,152,0,0.1);">
                                    <h5 class="mb-4" style="color: #ff9800; font-weight: 600;"><i class="fa fa-edit mr-2"></i>Edit Akun</h5>
                                    <form action="php/edit_profile.php" method="post" enctype="multipart/form-data">
                                        <div class="form-group mb-3">
                                            <label for="username" class="form-label" style="color: #333; font-weight: 500;"><i class="fa fa-user mr-2" style="color: #005a99;"></i>Nama Pengguna</label>
                                            <input type="text" class="form-control modern-input" id="username" name="username" value="<?php echo $username; ?>" required style="border: 2px solid #e9ecef; border-radius: 10px; padding: 12px 15px; transition: all 0.3s ease; background: rgba(255,255,255,0.8);">
                                        </div>
                                        <div class="form-group mb-3">
                                            <label for="password" class="form-label" style="color: #333; font-weight: 500;"><i class="fa fa-lock mr-2" style="color: #ff9800;"></i>Password Baru</label>
                                            <small class="text-muted d-block mb-2">(Kosongkan jika tidak ingin ganti)</small>
                                            <input type="password" class="form-control modern-input" id="password" name="password" placeholder="Password baru" style="border: 2px solid #e9ecef; border-radius: 10px; padding: 12px 15px; transition: all 0.3s ease; background: rgba(255,255,255,0.8);">
                                        </div>
                                        <div class="form-group mb-4">
                                            <label for="foto" class="form-label" style="color: #333; font-weight: 500;"><i class="fa fa-camera mr-2" style="color: #111;"></i>Foto Profil</label>
                                            <small class="text-muted d-block mb-2">(jpg/png/gif, max 2MB)</small>
                                            <input type="file" class="form-control modern-input" id="foto" name="foto" accept="image/*" onchange="previewImage(this)">
                                            <div id="imagePreview" class="mt-3" style="display: none;">
                                                <img id="preview" src="" alt="Preview" style="max-width: 200px; max-height: 200px; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,90,153,0.3);">
                                            </div>
                                        </div>
                                        <button type="submit" class="btn btn-gradient-success px-4 py-2" style="background: linear-gradient(45deg, #28a745, #20c997); border: none; border-radius: 25px; color: #fff; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(40,167,69,0.3);"><i class="fa fa-save mr-2"></i>Simpan Perubahan</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer text-center py-4" style="background: linear-gradient(90deg, rgba(17,17,17,0.05) 0%, rgba(0,90,153,0.05) 50%, rgba(255,152,0,0.05) 100%); border-top: 1px solid rgba(0,90,153,0.1);">
                    <a href="index.php" class="btn btn-outline-gradient mr-3 px-4 py-2" style="border: 2px solid; border-image: linear-gradient(45deg, #005a99, #ff9800) 1; color: #005a99; border-radius: 25px; font-weight: 600; transition: all 0.3s ease; text-decoration: none;"><i class="fa fa-home mr-2"></i>Kembali ke Beranda</a>
                    <a href="php/logout.php" class="btn btn-gradient-danger px-4 py-2" style="background: linear-gradient(45deg, #dc3545, #c82333); border: none; border-radius: 25px; color: #fff; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(220,53,69,0.3); text-decoration: none;"><i class="fa fa-sign-out mr-2"></i>Logout</a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'inc/footer.php'; ?>

<script>
// Preview gambar sebelum upload
function previewImage(input) {
    const preview = document.getElementById('preview');
    const previewContainer = document.getElementById('imagePreview');

    if (input.files && input.files[0]) {
        const file = input.files[0];

        // Validasi ukuran file (2MB)
        if (file.size > 2 * 1024 * 1024) {
            alert('Ukuran file terlalu besar! Maksimal 2MB.');
            input.value = '';
            previewContainer.style.display = 'none';
            return;
        }

        // Validasi tipe file
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            alert('Format file tidak didukung! Gunakan JPG, PNG, atau GIF.');
            input.value = '';
            previewContainer.style.display = 'none';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            preview.src = e.target.result;
            previewContainer.style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        previewContainer.style.display = 'none';
    }
}

// Validasi form sebelum submit
document.querySelector('form').addEventListener('submit', function(e) {
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;

    // Validasi username
    if (username.length < 3) {
        alert('Username minimal 3 karakter!');
        e.preventDefault();
        return;
    }

    // Validasi password jika diisi
    if (password && password.length < 6) {
        alert('Password minimal 6 karakter!');
        e.preventDefault();
        return;
    }

    // Konfirmasi submit
    if (!confirm('Apakah Anda yakin ingin menyimpan perubahan?')) {
        e.preventDefault();
        return;
    }
});

// Auto hide alerts setelah 5 detik
setTimeout(function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        if (alert.classList.contains('fade')) {
            alert.classList.remove('show');
        }
    });
}, 5000);
</script>

</body>
