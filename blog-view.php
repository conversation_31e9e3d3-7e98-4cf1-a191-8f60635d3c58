<?php 
session_start();
$logged = false;
if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
	 $logged = true;
	 $user_id = $_SESSION['user_id'];
}

if (isset($_GET['post_id'])) {

   	  include_once("admin/data/post.php");
        include_once("admin/data/comment.php");
        include_once("admin/data/category.php");
        include_once("admin/data/like.php");
        include_once("admin/data/user.php"); // Tambahkan agar fungsi getUserByID tersedia
        include_once("db_conn.php");
        $id = $_GET['post_id'];
        $post = getById($conn, $id);
        $comments = getCommentsByPostID($conn, $id);
        $categories = get5Categoies($conn); 

     if ($post == 0) {
     	  header("Location: blog.php");
	     exit;
     }
?>
<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Blog - <?=$post['post_title']?></title>
	<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
	<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
	<link rel="stylesheet" href="css/style.css">
	<style>
	body {
	    background: #fff !important;
	    min-height: 100vh;
	    font-family: 'Poppins', Arial, sans-serif;
	}
	.container-blog-view {
	    display: flex;
	    flex-direction: row;
	    align-items: flex-start;
	    gap: 32px;
	}
	.main-blog {
	    flex: 1 1 0%;
	    min-width: 0;
	}
	.aside-main {
	    width: 320px;
	    min-width: 240px;
	    max-width: 340px;
	    /* Hapus posisi fixed agar tidak ikut scroll */
	    position: static;
	    right: auto;
	    top: auto;
	    z-index: auto;
	}
	@media (max-width: 1200px) {
	    .aside-main { right: auto; }
	}
	@media (max-width: 991px) {
	    .container-blog-view { flex-direction: column; }
	    .aside-main {
	        width: 100%;
	        max-width: 100%;
	        margin-top: 32px;
	    }
	}
	.main-blog-card {
	    border-radius: 1.2rem;
	    box-shadow: 0 4px 24px 0 rgba(0,90,153,0.10);
	    background: #fff;
	    border: none;
	    margin-bottom: 32px;
	    padding-bottom: 0;
	    overflow: hidden;
	    transition: box-shadow 0.22s, transform 0.22s;
	}
	.main-blog-card:hover {
	    box-shadow: 0 12px 36px 0 rgba(0,90,153,0.18);
	    transform: translateY(-4px) scale(1.01);
	}
	.main-blog-card .card-title {
	    font-size: 1.35rem;
	    font-weight: 700;
	    color: #005a99;
	    margin-bottom: 12px;
	    letter-spacing: 0.5px;
	}
	.main-blog-card .card-text {
	    color: #444;
	    font-size: 1.08rem;
	    margin-bottom: 0;
	    line-height: 1.7;
	}
	.main-blog-card img.card-img-top {
	    max-height: 340px;
	    object-fit: cover;
	    border-radius: 1.2rem 1.2rem 0 0;
	    background: #000;
	}
	.category-aside {
	    border-radius: 1rem;
	    overflow: hidden;
	    background: #fff;
	    box-shadow: 0 4px 24px 0 rgba(0,90,153,0.08);
	    margin-bottom: 32px;
	}
	.category-aside .list-group-item {
	    border: none;
	    background: transparent;
	    transition: background 0.2s, color 0.2s;
	    font-weight: 600;
	    color: #005a99;
	    font-size: 1.05rem;
	}
	.category-aside .list-group-item.active,
	.category-aside .list-group-item:active {
	    background: linear-gradient(90deg, #005a99 0%, #ff8800 100%);
	    color: #fff;
	    border: none;
	    box-shadow: 0 2px 8px #005a9922;
	}
	.category-aside .list-group-item:not(.active):hover {
	    background: #f0f4fa;
	    color: #ff8800;
	}
	.react-btns .fa {
	    cursor: pointer;
	    margin-right: 0.5em;
	    transition: color 0.2s;
	    font-size: 1.1em;
	}
	.react-btns .fa.liked {
	    color: #e74c3c;
	}
	.react-btns .fa:hover {
	    color: #ff8800;
	}
	.comment {
	    background: #f8fafc;
	    border-radius: 0.7rem;
	    margin-bottom: 18px;
	    padding: 12px 16px;
	    box-shadow: 0 2px 8px #005a9911;
	}
	.comment img {
	    border-radius: 50%;
	    margin-right: 12px;
	    border: 2px solid #005a99;
	}
	.comment span {
	    font-weight: 600;
	    color: #005a99;
	}
	.comment p {
	    margin-bottom: 4px;
	    color: #333;
	}
	.comment small {
	    color: #888;
	}
	@media (max-width: 991px) {
	    .main-blog { margin-bottom: 32px; }
	    .aside-main { margin-top: 32px; }
	}
	@media (max-width: 767px) {
	    .main-blog-card img.card-img-top { max-height: 200px; }
	}
	</style>
</head>
<body>
	<?php 
        include 'inc/NavBar.php';
      ?>
    
    <div class="container mt-5 container-blog-view">
        <main class="main-blog">

  	   	<div class="card main-blog-card mb-5">
	  <img src="upload/blog/<?=$post['cover_url']?>" class="card-img-top" alt="...">
	  <div class="card-body">
	    <h5 class="card-title"><?=$post['post_title']?></h5>
	    <p class="card-text"><?=$post['post_text']?></p>
	    <hr>
<div class="d-flex justify-content-between">
<div class="react-btns">
	<?php 
		$post_id = isset($post['post_id']) ? $post['post_id'] : (isset($post['id']) ? $post['id'] : 0);
		if ($logged) {
			$liked = isLikedByUserID($conn, $post_id, $user_id);
		
        
        if($liked){
		 ?>
    	<i class="fa fa-thumbs-up liked like-btn" 
   	   post-id="<?=$post_id?>"
   	   liked="1"
   	   aria-hidden="true"></i>
       <?php }else{ ?>
      <i class="fa fa-thumbs-up like like-btn"
        post-id="<?=$post_id?>"
   	    liked="0"
        aria-hidden="true"></i>
    <?php } } else{ ?>
    <i class="fa fa-thumbs-up" aria-hidden="true"></i>
    <?php } ?>
   Likes (
    <span><?php 
     echo likeCountByPostID($conn, isset($post['post_id']) ? $post['post_id'] : (isset($post['id']) ? $post['id'] : 0));
      ?></span> )
      <i class="fa fa-comment" aria-hidden="true"></i> comments (
        <?php 
            echo CountByPostID($conn, isset($post['post_id']) ? $post['post_id'] : (isset($post['id']) ? $post['id'] : 0));
         ?>
        )
	    	
	    </div>	
	    <small class="text-body-secondary"><?= isset($post['crated_at']) ? htmlspecialchars($post['crated_at']) : (isset($post['created_at']) ? htmlspecialchars($post['created_at']) : '') ?></small>
</div>

<form action="php/comment.php" 
	      method="post"
	      id="comments">

	  <h5 class="mt-4 text-secondary">Add comment</h5>
	  <?php if(isset($_GET['error'])){ ?>
		<div class="alert alert-danger" role="alert">
		  <?php echo htmlspecialchars($_GET['error']); ?>
		</div>
	    <?php } ?>

	    <?php if(isset($_GET['success'])){ ?>
		<div class="alert alert-success" role="alert">
		  <?php echo htmlspecialchars($_GET['success']); ?>
		</div>
	    <?php } ?>
	  <div class="mb-3">
	    <input type="text" 
	           class="form-control"
	           name="comment">
	    <input type="text" 
	           class="form-control"
	           name="post_id"
	           value="<?=$id?>"
	           hidden>
	  </div>
	  <button type="submit" class="btn btn-primary">Comment</button>
	</form> <hr>
       <div>
  	<div class="comments">
	<?php if($comments != 0){ 
	  foreach ($comments as $comment) {
	  $u = getUserByID($conn, $comment['user_id']);
	?>
	<div class="comment d-flex">
	<div>
	    <?php 
	    // Cek apakah user punya foto profil dan path upload
	    $profileImg = (isset($u['profile_pic']) && !empty($u['profile_pic'])) ? 'upload/' . str_replace('\\', '/', $u['profile_pic']) : 'img/user-default.png';
	    ?>
	    <img src="<?=$profileImg?>" width="40" height="40">
	</div>
	<div class="p-2">
	   <span>@<?=$u['username'] ?></span>
	   <p><?=$comment['comment_text']?></p>
	   <small class="text-body-secondary"><?=$comment['created_at']?></small>
	</div>
	</div><hr>
		<?php }} ?>
  	</div>
    </div>       
  </div>
  </div>

</main>
        <aside class="aside-main">
            <div class="list-group category-aside">
                <a href="#" class="list-group-item list-group-item-action active" aria-current="true">
                    Category
                </a>
                <?php foreach ($categories as $category ) { ?>
                <a href="category.php?category_id=<?=$category['id']?>"
                   class="list-group-item list-group-item-action">
                    <?php echo $category['category']; ?>
                </a>
                <?php } ?>
            </div>
        </aside>
    </div>
   <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
  
   <script>
   	 $(document).ready(function(){
			  $(".like-btn").click(function(){
			     var post_id = $(this).attr('post-id');
			     var liked = $(this).attr('liked');

			     if (liked == 1) {
                 $(this).attr('liked', '0');
                 $(this).removeClass('liked');
			     }else {
                 $(this).attr('liked', '1');
                 $(this).addClass('liked');
			     }
			     $(this).next().load("ajax/like-unlike.php",
			     	{
			     		post_id: post_id
			     	});
			  });
		  });
   </script>

	<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" integrity="sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz" crossorigin="anonymous"></script>

    <?php   include 'inc/footer.php'; ?>
</body>
</html>
<?php }else {
	header("Location: blog.php");
	exit;
} ?>