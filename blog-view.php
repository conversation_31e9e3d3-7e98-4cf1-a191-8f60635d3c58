<?php 
session_start();
$logged = false;
if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
	 $logged = true;
	 $user_id = $_SESSION['user_id'];
}

if (isset($_GET['post_id'])) {

   	  include_once("admin/data/post.php");
        include_once("admin/data/comment.php");
        include_once("admin/data/category.php");
        include_once("admin/data/like.php");
        include_once("admin/data/user.php");
        include_once("db_conn.php");

        $id = $_GET['post_id'];
        $post = getById($conn, $id);
        $comments = getCommentsByPostID($conn, $id);
        $categories = get5Categoies($conn);

        // Get post author information
        $author = null;
        if (isset($post['user_id']) && $post['user_id']) {
            $author = getUserByID($conn, $post['user_id']);
        }

        // Get category name
        $category_name = '';
        if (isset($post['category_id']) && $post['category_id']) {
            $category = getCategoryById($conn, $post['category_id']);
            $category_name = $category ? $category['category'] : '';
        }

     if ($post == 0) {
     	  header("Location: blog.php");
	     exit;
     }
?>
<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Blog - <?=$post['post_title']?></title>
	<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
	<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
	<link rel="stylesheet" href="css/style.css">
	<style>
	body {
	    background: #fff !important;
	    min-height: 100vh;
	    font-family: 'Poppins', Arial, sans-serif;
	}
	.container-blog-view {
	    display: flex;
	    flex-direction: row;
	    align-items: flex-start;
	    gap: 32px;
	    max-width: 1200px;
	    margin: 0 auto;
	    padding: 0 15px;
	}
	.main-blog {
	    flex: 1 1 0%;
	    min-width: 0;
	}
	.aside-main {
	    width: 320px;
	    min-width: 240px;
	    max-width: 340px;
	    position: static;
	    right: auto;
	    top: auto;
	    z-index: auto;
	}
	.post-meta {
	    display: flex;
	    flex-wrap: wrap;
	    align-items: center;
	    gap: 12px;
	    margin-bottom: 16px;
	}
	.post-meta .badge {
	    font-size: 0.85rem;
	    padding: 6px 12px;
	    border-radius: 20px;
	}
	.post-meta .text-muted {
	    font-size: 0.9rem;
	}
	@media (max-width: 1200px) {
	    .container-blog-view {
	        max-width: 100%;
	        padding: 0 20px;
	    }
	    .aside-main {
	        width: 280px;
	        min-width: 220px;
	    }
	}
	@media (max-width: 991px) {
	    .container-blog-view {
	        flex-direction: column;
	        gap: 24px;
	    }
	    .aside-main {
	        width: 100%;
	        max-width: 100%;
	        margin-top: 0;
	    }
	    .post-meta {
	        flex-direction: column;
	        align-items: flex-start;
	        gap: 8px;
	    }
	}
	@media (max-width: 768px) {
	    .container-blog-view {
	        padding: 0 15px;
	        gap: 20px;
	    }
	    .main-blog-card .card-title {
	        font-size: 1.2rem;
	    }
	    .main-blog-card .card-text {
	        font-size: 1rem;
	    }
	}
	.main-blog-card {
	    border-radius: 1.2rem;
	    box-shadow: 0 4px 24px 0 rgba(0,90,153,0.10);
	    background: #fff;
	    border: none;
	    margin-bottom: 32px;
	    padding-bottom: 0;
	    overflow: hidden;
	    transition: box-shadow 0.22s, transform 0.22s;
	}
	.main-blog-card:hover {
	    box-shadow: 0 12px 36px 0 rgba(0,90,153,0.18);
	    transform: translateY(-4px) scale(1.01);
	}
	.main-blog-card .card-title {
	    font-size: 1.35rem;
	    font-weight: 700;
	    color: #005a99;
	    margin-bottom: 12px;
	    letter-spacing: 0.5px;
	}
	.main-blog-card .card-text {
	    color: #444;
	    font-size: 1.08rem;
	    margin-bottom: 0;
	    line-height: 1.7;
	}
	.main-blog-card img.card-img-top {
	    max-height: 340px;
	    object-fit: cover;
	    border-radius: 1.2rem 1.2rem 0 0;
	    background: #000;
	}
	.category-aside {
	    border-radius: 1rem;
	    overflow: hidden;
	    background: #fff;
	    box-shadow: 0 4px 24px 0 rgba(0,90,153,0.08);
	    margin-bottom: 32px;
	}
	.category-aside .list-group-item {
	    border: none;
	    background: transparent;
	    transition: background 0.2s, color 0.2s;
	    font-weight: 600;
	    color: #005a99;
	    font-size: 1.05rem;
	}
	.category-aside .list-group-item.active,
	.category-aside .list-group-item:active {
	    background: linear-gradient(90deg, #005a99 0%, #ff8800 100%);
	    color: #fff;
	    border: none;
	    box-shadow: 0 2px 8px #005a9922;
	}
	.category-aside .list-group-item:not(.active):hover {
	    background: #f0f4fa;
	    color: #ff8800;
	}
	.react-btns .fa {
	    cursor: pointer;
	    margin-right: 0.5em;
	    transition: color 0.2s;
	    font-size: 1.1em;
	}
	.react-btns .fa.liked {
	    color: #e74c3c;
	}
	.react-btns .fa:hover {
	    color: #ff8800;
	}
	.comment {
	    background: #f8fafc;
	    border-radius: 0.7rem;
	    margin-bottom: 18px;
	    padding: 16px;
	    box-shadow: 0 2px 8px #005a9911;
	    border-left: 3px solid #005a99;
	}
	.comment img {
	    border-radius: 50%;
	    margin-right: 12px;
	    border: 2px solid #005a99;
	    width: 40px;
	    height: 40px;
	    object-fit: cover;
	}
	.comment .comment-header {
	    display: flex;
	    align-items: center;
	    margin-bottom: 8px;
	}
	.comment .comment-author {
	    font-weight: 600;
	    color: #005a99;
	    margin-right: 12px;
	}
	.comment .comment-date {
	    color: #888;
	    font-size: 0.85rem;
	}
	.comment .comment-text {
	    margin-bottom: 0;
	    color: #333;
	    line-height: 1.6;
	}
	.comments-section {
	    margin-top: 32px;
	}
	.comments-title {
	    color: #005a99;
	    font-weight: 600;
	    margin-bottom: 20px;
	    border-bottom: 2px solid #005a99;
	    padding-bottom: 8px;
	}
	@media (max-width: 991px) {
	    .main-blog { margin-bottom: 32px; }
	    .aside-main { margin-top: 32px; }
	}
	@media (max-width: 767px) {
	    .main-blog-card img.card-img-top { max-height: 200px; }
	    .post-meta {
	        gap: 8px;
	    }
	    .post-meta .badge {
	        font-size: 0.75rem;
	        padding: 4px 8px;
	    }
	    .comment img {
	        width: 32px;
	        height: 32px;
	    }
	    .comment .comment-author {
	        font-size: 0.9rem;
	    }
	    .comment .comment-date {
	        font-size: 0.8rem;
	    }
	}
	@media (max-width: 576px) {
	    .container-blog-view {
	        padding: 0 10px;
	    }
	    .main-blog-card .card-title {
	        font-size: 1.1rem;
	    }
	    .main-blog-card .card-text {
	        font-size: 0.95rem;
	    }
	    .react-btns {
	        font-size: 0.9rem;
	    }
	    .comment {
	        padding: 12px;
	    }
	}
	</style>
</head>
<body>
	<?php 
        include 'inc/NavBar.php';
      ?>
    
    <div class="container mt-5 container-blog-view">
        <main class="main-blog">

  	   	<div class="card main-blog-card mb-5">
	  <img src="upload/blog/<?=$post['cover_url']?>" class="card-img-top" alt="<?=htmlspecialchars($post['post_title'])?>">
	  <div class="card-body">
	    <div class="post-meta mb-3">
	        <?php if ($category_name): ?>
	            <span class="badge bg-primary me-2">
	                <i class="fa fa-folder-o me-1"></i><?=htmlspecialchars($category_name)?>
	            </span>
	        <?php endif; ?>
	        <?php if ($author): ?>
	            <span class="text-muted">
	                <i class="fa fa-user me-1"></i>By <?=htmlspecialchars($author['fname'] ?: $author['username'])?>
	            </span>
	        <?php endif; ?>
	    </div>
	    <h5 class="card-title"><?=htmlspecialchars($post['post_title'])?></h5>
	    <div class="card-text"><?=nl2br(htmlspecialchars($post['post_text']))?></div>
	    <hr>
<div class="d-flex justify-content-between">
<div class="react-btns">
	<?php
		$post_id = $post['id'] ?? $post['post_id'] ?? 0;
		if ($logged) {
			$liked = isLikedByUserID($conn, $post_id, $user_id);
		
        
        if($liked){
		 ?>
    	<i class="fa fa-thumbs-up liked like-btn" 
   	   post-id="<?=$post_id?>"
   	   liked="1"
   	   aria-hidden="true"></i>
       <?php }else{ ?>
      <i class="fa fa-thumbs-up like like-btn"
        post-id="<?=$post_id?>"
   	    liked="0"
        aria-hidden="true"></i>
    <?php } } else{ ?>
    <i class="fa fa-thumbs-up" aria-hidden="true"></i>
    <?php } ?>
   Likes (
    <span><?php
     echo likeCountByPostID($conn, $post_id);
      ?></span> )
      <i class="fa fa-comment" aria-hidden="true"></i> comments (
        <?php
            echo CountByPostID($conn, $post_id);
         ?>
        )
	    	
	    </div>	
	    <small class="text-body-secondary">
	        <i class="fa fa-calendar me-1"></i>
	        <?php
	            $created_at = $post['created_at'] ?? $post['crated_at'] ?? '';
	            if ($created_at) {
	                echo date('d M Y, H:i', strtotime($created_at));
	            }
	        ?>
	    </small>
</div>

<form action="php/comment.php" 
	      method="post"
	      id="comments">

	  <h5 class="mt-4 text-secondary">Add comment</h5>
	  <?php if(isset($_GET['error'])){ ?>
		<div class="alert alert-danger" role="alert">
		  <?php echo htmlspecialchars($_GET['error']); ?>
		</div>
	    <?php } ?>

	    <?php if(isset($_GET['success'])){ ?>
		<div class="alert alert-success" role="alert">
		  <?php echo htmlspecialchars($_GET['success']); ?>
		</div>
	    <?php } ?>
	  <div class="mb-3">
	    <input type="text" 
	           class="form-control"
	           name="comment">
	    <input type="text" 
	           class="form-control"
	           name="post_id"
	           value="<?=$id?>"
	           hidden>
	  </div>
	  <button type="submit" class="btn btn-primary">Comment</button>
	</form> <hr>
       <div class="comments-section">
       <h5 class="comments-title">
           <i class="fa fa-comments me-2"></i>Comments (<?=count($comments)?>)
       </h5>
  	<div class="comments">
	<?php if($comments != 0){
	  foreach ($comments as $comment) {
	  $u = getUserByID($conn, $comment['user_id']);
	?>
	<div class="comment">
	    <div class="comment-header">
	        <?php
	        // Cek apakah user punya foto profil dan path upload
	        $profileImg = (isset($u['profile_pic']) && !empty($u['profile_pic'])) ? 'upload/' . str_replace('\\', '/', $u['profile_pic']) : 'img/user-default.png';
	        ?>
	        <img src="<?=$profileImg?>" alt="<?=htmlspecialchars($u['username'])?>">
	        <div>
	            <div class="comment-author">@<?=htmlspecialchars($u['username'])?></div>
	            <div class="comment-date">
	                <i class="fa fa-clock-o me-1"></i>
	                <?=date('d M Y, H:i', strtotime($comment['created_at']))?>
	            </div>
	        </div>
	    </div>
	    <div class="comment-text"><?=nl2br(htmlspecialchars($comment['comment_text']))?></div>
	</div>
		<?php }
		} else { ?>
		    <div class="text-center text-muted py-4">
		        <i class="fa fa-comment-o fa-2x mb-2"></i>
		        <p>Belum ada komentar. Jadilah yang pertama berkomentar!</p>
		    </div>
		<?php } ?>
  	</div>
    </div>
  </div>
  </div>

</main>
        <aside class="aside-main">
            <div class="list-group category-aside">
                <a href="#" class="list-group-item list-group-item-action active" aria-current="true">
                    Category
                </a>
                <?php foreach ($categories as $category ) { ?>
                <a href="category.php?category_id=<?=$category['id']?>"
                   class="list-group-item list-group-item-action">
                    <?php echo $category['category']; ?>
                </a>
                <?php } ?>
            </div>
        </aside>
    </div>
   <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
  
   <script>
   	 $(document).ready(function(){
			  $(".like-btn").click(function(){
			     var post_id = $(this).attr('post-id');
			     var liked = $(this).attr('liked');

			     if (liked == 1) {
                 $(this).attr('liked', '0');
                 $(this).removeClass('liked');
			     }else {
                 $(this).attr('liked', '1');
                 $(this).addClass('liked');
			     }
			     $(this).next().load("ajax/like-unlike.php",
			     	{
			     		post_id: post_id
			     	});
			  });
		  });
   </script>

	<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" integrity="sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz" crossorigin="anonymous"></script>

    <?php   include 'inc/footer.php'; ?>
</body>
</html>
<?php }else {
	header("Location: blog.php");
	exit;
} ?>