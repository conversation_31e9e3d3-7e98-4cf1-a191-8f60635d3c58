<?php 
session_start();
$logged = false;
if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
	 $logged = true;
	 $user_id = $_SESSION['user_id'];
}

if (isset($_GET['post_id'])) {

   	  include_once("admin/data/post.php");
        include_once("admin/data/comment.php");
        include_once("admin/data/category.php");
        include_once("admin/data/like.php");
        include_once("admin/data/user.php");
        include_once("db_conn.php");

        $id = $_GET['post_id'];
        $post = getById($conn, $id);
        $comments = getCommentsByPostID($conn, $id);
        $categories = get5Categoies($conn);

        // Get post author information
        $author = null;
        if (isset($post['user_id']) && $post['user_id']) {
            $author = getUserByID($conn, $post['user_id']);
        }

        // Get category name
        $category_name = '';
        if (isset($post['category_id']) && $post['category_id']) {
            $category = getCategoryById($conn, $post['category_id']);
            $category_name = $category ? $category['category'] : '';
        }

     if ($post == 0) {
     	  header("Location: blog.php");
	     exit;
     }
?>
<!DOCTYPE html>
<html lang="id">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta name="description" content="<?=htmlspecialchars(substr(strip_tags($post['post_text']), 0, 160))?>">
	<meta name="keywords" content="<?=htmlspecialchars($category_name)?>, blog, artikel, UKM Panahan Gendewa Geni">
	<meta name="author" content="<?=htmlspecialchars($author['fname'] ?? $author['username'] ?? 'Admin')?>">

	<!-- Open Graph Meta Tags -->
	<meta property="og:title" content="<?=htmlspecialchars($post['post_title'])?>">
	<meta property="og:description" content="<?=htmlspecialchars(substr(strip_tags($post['post_text']), 0, 160))?>">
	<meta property="og:image" content="<?=isset($_SERVER['HTTPS']) ? 'https' : 'http'?>://<?=$_SERVER['HTTP_HOST']?>/upload/blog/<?=$post['cover_url']?>">
	<meta property="og:type" content="article">

	<title><?=htmlspecialchars($post['post_title'])?> - UKM Panahan Gendewa Geni</title>

	<!-- CSS Libraries -->
	<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
	<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">
	<link rel="stylesheet" href="css/style.css">
	<link rel="stylesheet" href="dropdown_animations.css">
	<style>
	body {
	    background: #fff !important;
	    min-height: 100vh;
	    font-family: 'Poppins', Arial, sans-serif;
	}
	.container-blog-view {
	    display: flex;
	    flex-direction: row;
	    align-items: flex-start;
	    gap: 32px;
	    max-width: 1200px;
	    margin: 0 auto;
	    padding: 0 15px;
	}
	.main-blog {
	    flex: 1 1 0%;
	    min-width: 0;
	}
	.aside-main {
	    width: 320px;
	    min-width: 240px;
	    max-width: 340px;
	    position: static;
	    right: auto;
	    top: auto;
	    z-index: auto;
	}
	.post-meta {
	    display: flex;
	    flex-wrap: wrap;
	    align-items: center;
	    gap: 12px;
	    margin-bottom: 16px;
	}
	.post-meta .badge {
	    font-size: 0.85rem;
	    padding: 6px 12px;
	    border-radius: 20px;
	}
	.post-meta .text-muted {
	    font-size: 0.9rem;
	}
	@media (max-width: 1200px) {
	    .container-blog-view {
	        max-width: 100%;
	        padding: 0 20px;
	    }
	    .aside-main {
	        width: 280px;
	        min-width: 220px;
	    }
	}
	@media (max-width: 991px) {
	    .container-blog-view {
	        flex-direction: column;
	        gap: 24px;
	    }
	    .aside-main {
	        width: 100%;
	        max-width: 100%;
	        margin-top: 0;
	    }
	    .post-meta {
	        flex-direction: column;
	        align-items: flex-start;
	        gap: 8px;
	    }
	}
	@media (max-width: 768px) {
	    .container-blog-view {
	        padding: 0 15px;
	        gap: 20px;
	    }
	    .main-blog-card .card-title {
	        font-size: 1.2rem;
	    }
	    .main-blog-card .card-text {
	        font-size: 1rem;
	    }
	}
	.main-blog-card {
	    border-radius: 1.2rem;
	    box-shadow: 0 4px 24px 0 rgba(0,90,153,0.10);
	    background: #fff;
	    border: none;
	    margin-bottom: 32px;
	    padding-bottom: 0;
	    overflow: hidden;
	    transition: box-shadow 0.22s, transform 0.22s;
	}
	.main-blog-card:hover {
	    box-shadow: 0 12px 36px 0 rgba(0,90,153,0.18);
	    transform: translateY(-4px) scale(1.01);
	}
	.main-blog-card .card-title {
	    font-size: 1.35rem;
	    font-weight: 700;
	    color: #005a99;
	    margin-bottom: 12px;
	    letter-spacing: 0.5px;
	}
	.main-blog-card .card-text {
	    color: #444;
	    font-size: 1.08rem;
	    margin-bottom: 0;
	    line-height: 1.7;
	}
	.main-blog-card img.card-img-top {
	    max-height: 340px;
	    object-fit: cover;
	    border-radius: 1.2rem 1.2rem 0 0;
	    background: #000;
	}
	.category-aside {
	    border-radius: 1rem;
	    overflow: hidden;
	    background: #fff;
	    box-shadow: 0 4px 24px 0 rgba(0,90,153,0.08);
	    margin-bottom: 32px;
	}
	.category-aside .list-group-item {
	    border: none;
	    background: transparent;
	    transition: background 0.2s, color 0.2s;
	    font-weight: 600;
	    color: #005a99;
	    font-size: 1.05rem;
	}
	.category-aside .list-group-item.active,
	.category-aside .list-group-item:active {
	    background: linear-gradient(90deg, #005a99 0%, #ff8800 100%);
	    color: #fff;
	    border: none;
	    box-shadow: 0 2px 8px #005a9922;
	}
	.category-aside .list-group-item:not(.active):hover {
	    background: #f0f4fa;
	    color: #ff8800;
	}
	.react-btns .fa {
	    cursor: pointer;
	    margin-right: 0.5em;
	    transition: color 0.2s;
	    font-size: 1.1em;
	}
	.react-btns .fa.liked {
	    color: #e74c3c;
	}
	.react-btns .fa:hover {
	    color: #ff8800;
	}
	.comment {
	    background: #f8fafc;
	    border-radius: 0.7rem;
	    margin-bottom: 18px;
	    padding: 16px;
	    box-shadow: 0 2px 8px #005a9911;
	    border-left: 3px solid #005a99;
	}
	.comment img {
	    border-radius: 50%;
	    margin-right: 12px;
	    border: 2px solid #005a99;
	    width: 40px;
	    height: 40px;
	    object-fit: cover;
	}
	.comment .comment-header {
	    display: flex;
	    align-items: center;
	    margin-bottom: 8px;
	}
	.comment .comment-author {
	    font-weight: 600;
	    color: #005a99;
	    margin-right: 12px;
	}
	.comment .comment-date {
	    color: #888;
	    font-size: 0.85rem;
	}
	.comment .comment-text {
	    margin-bottom: 0;
	    color: #333;
	    line-height: 1.6;
	}
	.comments-section {
	    margin-top: 32px;
	}
	.comments-title {
	    color: #005a99;
	    font-weight: 600;
	    margin-bottom: 20px;
	    border-bottom: 2px solid #005a99;
	    padding-bottom: 8px;
	}
	@media (max-width: 991px) {
	    .main-blog { margin-bottom: 32px; }
	    .aside-main { margin-top: 32px; }
	}
	@media (max-width: 767px) {
	    .main-blog-card img.card-img-top { max-height: 200px; }
	    .post-meta {
	        gap: 8px;
	    }
	    .post-meta .badge {
	        font-size: 0.75rem;
	        padding: 4px 8px;
	    }
	    .comment img {
	        width: 32px;
	        height: 32px;
	    }
	    .comment .comment-author {
	        font-size: 0.9rem;
	    }
	    .comment .comment-date {
	        font-size: 0.8rem;
	    }
	}
	@media (max-width: 576px) {
	    .container-blog-view {
	        padding: 0 10px;
	    }
	    .main-blog-card .card-title {
	        font-size: 1.1rem;
	    }
	    .main-blog-card .card-text {
	        font-size: 0.95rem;
	    }
	    .react-btns {
	        font-size: 0.9rem;
	    }
	    .comment {
	        padding: 12px;
	    }
	}

	/* Blog Hero Section */
	.blog-hero {
	    background: linear-gradient(135deg, #005a99 0%, #ff9800 100%);
	    color: white;
	    padding: 120px 0 60px;
	    margin-top: 0px;
	    position: relative;
	    overflow: hidden;
	}

	.blog-hero::before {
	    content: '';
	    position: absolute;
	    top: 0;
	    left: 0;
	    right: 0;
	    bottom: 0;
	    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
	    opacity: 0.3;
	}

	.blog-hero h1 {
	    font-size: 2.5rem;
	    font-weight: 700;
	    margin-bottom: 20px;
	    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
	    position: relative;
	    z-index: 2;
	}

	.blog-hero .breadcrumb {
	    background: rgba(255,255,255,0.15);
	    border-radius: 25px;
	    padding: 10px 20px;
	    backdrop-filter: blur(10px);
	    position: relative;
	    z-index: 2;
	}

	.blog-hero .breadcrumb-item a {
	    color: rgba(255,255,255,0.9);
	    text-decoration: none;
	    transition: color 0.3s ease;
	}

	.blog-hero .breadcrumb-item a:hover {
	    color: white;
	}

	.blog-hero .breadcrumb-item.active {
	    color: white;
	    font-weight: 600;
	}

	.hero-meta {
	    margin-top: 20px;
	    position: relative;
	    z-index: 2;
	}

	.hero-meta .badge {
	    font-size: 0.9rem;
	    padding: 8px 16px;
	    border-radius: 20px;
	}

	/* Enhanced React Buttons */
	.react-btns {
	    display: flex;
	    align-items: center;
	    gap: 15px;
	    margin-top: 20px;
	    padding: 20px;
	    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	    border-radius: 15px;
	    border: 1px solid #dee2e6;
	}

	.react-btns .btn {
	    border-radius: 25px;
	    padding: 10px 20px;
	    font-size: 0.95rem;
	    font-weight: 600;
	    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
	    position: relative;
	    overflow: hidden;
	    border: 2px solid;
	}

	.react-btns .btn::before {
	    content: '';
	    position: absolute;
	    top: 50%;
	    left: 50%;
	    width: 0;
	    height: 0;
	    background: rgba(255,255,255,0.3);
	    border-radius: 50%;
	    transform: translate(-50%, -50%);
	    transition: width 0.6s, height 0.6s;
	}

	.react-btns .btn:active::before {
	    width: 300px;
	    height: 300px;
	}

	.react-btns .btn-outline-primary {
	    border-color: #005a99;
	    color: #005a99;
	    background: white;
	}

	.react-btns .btn-outline-primary:hover,
	.react-btns .btn-outline-primary.liked {
	    background: linear-gradient(135deg, #005a99, #0066cc);
	    border-color: #005a99;
	    color: white;
	    transform: translateY(-2px) scale(1.05);
	    box-shadow: 0 8px 25px rgba(0,90,153,0.4);
	}

	.react-btns .btn-outline-danger {
	    border-color: #dc3545;
	    color: #dc3545;
	    background: white;
	}

	.react-btns .btn-outline-danger:hover {
	    background: linear-gradient(135deg, #dc3545, #ff4757);
	    border-color: #dc3545;
	    color: white;
	    transform: translateY(-2px) scale(1.05);
	    box-shadow: 0 8px 25px rgba(220,53,69,0.4);
	}

	/* Enhanced Comments Section */
	.comments-section {
	    background: white;
	    border-radius: 20px;
	    padding: 40px;
	    margin-top: 40px;
	    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
	    border: 1px solid #e9ecef;
	}

	.comments-title {
	    color: #005a99;
	    font-weight: 700;
	    font-size: 1.8rem;
	    margin-bottom: 30px;
	    position: relative;
	    padding-bottom: 15px;
	}

	.comments-title::after {
	    content: '';
	    position: absolute;
	    bottom: 0;
	    left: 0;
	    width: 60px;
	    height: 4px;
	    background: linear-gradient(135deg, #005a99, #ff9800);
	    border-radius: 2px;
	}

	.comment {
	    background: white;
	    border-radius: 15px;
	    padding: 25px;
	    margin-bottom: 20px;
	    border: 1px solid #e9ecef;
	    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
	    transition: all 0.3s ease;
	    position: relative;
	    overflow: hidden;
	}

	.comment::before {
	    content: '';
	    position: absolute;
	    left: 0;
	    top: 0;
	    bottom: 0;
	    width: 4px;
	    background: linear-gradient(135deg, #005a99, #ff9800);
	}

	.comment:hover {
	    background: #f8f9fa;
	    transform: translateY(-2px);
	    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
	}

	.comment img {
	    border-radius: 50%;
	    margin-right: 15px;
	    border: 3px solid #005a99;
	    width: 50px;
	    height: 50px;
	    object-fit: cover;
	    box-shadow: 0 4px 12px rgba(0,90,153,0.3);
	}

	.comment .comment-header {
	    display: flex;
	    align-items: center;
	    margin-bottom: 12px;
	}

	.comment .comment-author {
	    font-weight: 700;
	    color: #005a99;
	    margin-right: 15px;
	    font-size: 1.1rem;
	}

	.comment .comment-date {
	    color: #888;
	    font-size: 0.9rem;
	    background: #f1f3f4;
	    padding: 4px 12px;
	    border-radius: 15px;
	}

	.comment .comment-text {
	    margin-bottom: 0;
	    color: #444;
	    line-height: 1.7;
	    font-size: 1rem;
	}

	/* Responsive Design */
	@media (max-width: 991px) {
	    .blog-hero {
	        padding: 100px 0 50px;
	        margin-top: 0px;
	    }

	    .blog-hero h1 {
	        font-size: 2rem;
	    }

	    .react-btns {
	        flex-direction: column;
	        gap: 15px;
	    }

	    .react-btns .btn {
	        width: 100%;
	        justify-content: center;
	    }

	    .comments-section {
	        padding: 25px;
	    }

	    .comment {
	        padding: 20px;
	    }
	}

	@media (max-width: 576px) {
	    .blog-hero h1 {
	        font-size: 1.6rem;
	    }

	    .blog-hero {
	        padding: 80px 0 40px;
	    }

	    .comments-title {
	        font-size: 1.5rem;
	    }
	}
	</style>
</head>
<body>
	<?php include 'inc/NavBar.php'; ?>

    <!-- Blog Hero Section -->
    <section class="blog-hero">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                            <li class="breadcrumb-item"><a href="blog.php">Blog</a></li>
                            <li class="breadcrumb-item active" aria-current="page"><?=htmlspecialchars($post['post_title'])?></li>
                        </ol>
                    </nav>
                    <h1><?=htmlspecialchars($post['post_title'])?></h1>
                    <div class="hero-meta">
                        <?php if ($category_name): ?>
                            <span class="badge bg-light text-dark me-3">
                                <i class="fa fa-folder-o me-1"></i><?=htmlspecialchars($category_name)?>
                            </span>
                        <?php endif; ?>
                        <?php if ($author): ?>
                            <span class="text-light">
                                <i class="fa fa-user me-1"></i>By <?=htmlspecialchars($author['fname'] ?: $author['username'])?>
                            </span>
                        <?php endif; ?>
                        <span class="text-light ms-3">
                            <i class="fa fa-calendar me-1"></i><?=date('d M Y', strtotime($post['created_at']))?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="container container-blog-view">
        <main class="main-blog">

  	   	<div class="card main-blog-card mb-5">
	  <img src="upload/blog/<?=$post['cover_url']?>" class="card-img-top" alt="<?=htmlspecialchars($post['post_title'])?>">
	  <div class="card-body">
	    <div class="card-text"><?=nl2br(htmlspecialchars($post['post_text']))?></div>
	    <hr>
<!-- React Buttons Section -->
<div class="react-btns">
	<?php
		$post_id = $post['id'] ?? $post['post_id'] ?? 0;
		if ($logged) {
			$liked = isLikedByUserID($conn, $post_id, $user_id);

        if($liked){
		 ?>
    	<button class="btn btn-outline-primary liked like-btn"
   	   		post-id="<?=$post_id?>"
   	   		liked="1">
   	   		<i class="fa fa-thumbs-up me-2"></i>
   	   		Liked (<span class="like-count"><?=likeCountByPostID($conn, $post_id)?></span>)
   	   	</button>
       <?php }else{ ?>
      <button class="btn btn-outline-primary like-btn"
        	post-id="<?=$post_id?>"
   	    	liked="0">
   	    	<i class="fa fa-thumbs-up me-2"></i>
   	    	Like (<span class="like-count"><?=likeCountByPostID($conn, $post_id)?></span>)
   	    </button>
    <?php } } else{ ?>
    <button class="btn btn-outline-primary" disabled>
    	<i class="fa fa-thumbs-up me-2"></i>
    	Like (<span class="like-count"><?=likeCountByPostID($conn, $post_id)?></span>)
    </button>
    <?php } ?>

    <button class="btn btn-outline-secondary" onclick="scrollToComments()">
    	<i class="fa fa-comment me-2"></i>
    	Comments (<?=CountByPostID($conn, $post_id)?>)
    </button>

    <button class="btn btn-outline-info" onclick="sharePost()">
    	<i class="fa fa-share me-2"></i>
    	Share
    </button>
</div>

<!-- Post Date -->
<div class="post-date mt-3">
    <small class="text-muted">
        <i class="fa fa-calendar me-1"></i>
        <?php
            $created_at = $post['created_at'] ?? $post['crated_at'] ?? '';
            if ($created_at) {
                echo date('d M Y, H:i', strtotime($created_at));
            }
        ?>
    </small>
</div>
	  </div>
	</div>
        </main>

        <!-- Sidebar -->
        <aside class="aside-main">
            <!-- Categories Widget -->
            <div class="category-aside">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="fa fa-folder-o me-2"></i>Categories</h6>
                </div>
                <div class="list-group list-group-flush">
                    <?php foreach($categories as $category): ?>
                        <a href="category.php?id=<?=$category['id']?>"
                           class="list-group-item list-group-item-action">
                            <i class="fa fa-angle-right me-2"></i><?=htmlspecialchars($category['category'])?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Recent Posts Widget -->
            <div class="category-aside">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0"><i class="fa fa-clock-o me-2"></i>Recent Posts</h6>
                </div>
                <div class="list-group list-group-flush">
                    <?php
                    include_once("admin/data/post.php");
                    $recent_posts = getRecentPosts($conn, 5);
                    if($recent_posts):
                    foreach($recent_posts as $recent_post):
                    ?>
                        <a href="blog-view.php?post_id=<?=$recent_post['id']?>"
                           class="list-group-item list-group-item-action">
                            <div class="d-flex">
                                <img src="upload/blog/<?=$recent_post['cover_url']?>"
                                     alt="<?=htmlspecialchars($recent_post['post_title'])?>"
                                     style="width: 50px; height: 50px; object-fit: cover; border-radius: 8px;"
                                     class="me-3">
                                <div>
                                    <h6 class="mb-1 text-truncate" style="max-width: 200px;">
                                        <?=htmlspecialchars($recent_post['post_title'])?>
                                    </h6>
                                    <small class="text-muted">
                                        <?=date('d M Y', strtotime($recent_post['created_at']))?>
                                    </small>
                                </div>
                            </div>
                        </a>
                    <?php endforeach; endif; ?>
                </div>
            </div>
        </aside>
    </div>

<!-- Comments Section -->
<div class="container">
    <div class="comments-section">
        <form action="php/comment.php"
              method="post"
              id="comments">

	  <h5 class="mt-4 text-secondary">Add comment</h5>
	  <?php if(isset($_GET['error'])){ ?>
		<div class="alert alert-danger" role="alert">
		  <?php echo htmlspecialchars($_GET['error']); ?>
		</div>
	    <?php } ?>

	    <?php if(isset($_GET['success'])){ ?>
		<div class="alert alert-success" role="alert">
		  <?php echo htmlspecialchars($_GET['success']); ?>
		</div>
	    <?php } ?>
	  <div class="mb-3">
	    <input type="text" 
	           class="form-control"
	           name="comment">
	    <input type="text" 
	           class="form-control"
	           name="post_id"
	           value="<?=$id?>"
	           hidden>
	  </div>
	  <button type="submit" class="btn btn-primary">Comment</button>
	</form> <hr>
       <div class="comments-section">
       <h5 class="comments-title">
           <i class="fa fa-comments me-2"></i>Comments (<?=count($comments)?>)
       </h5>
  	<div class="comments">
	<?php if($comments != 0){
	  foreach ($comments as $comment) {
	  $u = getUserByID($conn, $comment['user_id']);
	?>
	<div class="comment">
	    <div class="comment-header">
	        <?php
	        // Cek apakah user punya foto profil dan path upload
	        $profileImg = (isset($u['profile_pic']) && !empty($u['profile_pic'])) ? 'upload/' . str_replace('\\', '/', $u['profile_pic']) : 'img/user-default.png';
	        ?>
	        <img src="<?=$profileImg?>" alt="<?=htmlspecialchars($u['username'])?>">
	        <div>
	            <div class="comment-author">@<?=htmlspecialchars($u['username'])?></div>
	            <div class="comment-date">
	                <i class="fa fa-clock-o me-1"></i>
	                <?=date('d M Y, H:i', strtotime($comment['created_at']))?>
	            </div>
	        </div>
	    </div>
	    <div class="comment-text"><?=nl2br(htmlspecialchars($comment['comment_text']))?></div>
	</div>
		<?php }
		} else { ?>
		    <div class="text-center text-muted py-4">
		        <i class="fa fa-comment-o fa-2x mb-2"></i>
		        <p>Belum ada komentar. Jadilah yang pertama berkomentar!</p>
		    </div>
		<?php } ?>
  	</div>
    </div>
</div>
   <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
  
   <script>
   	 $(document).ready(function(){
			  $(".like-btn").click(function(){
			     var post_id = $(this).attr('post-id');
			     var liked = $(this).attr('liked');
			     var $btn = $(this);
			     var $count = $btn.find('.like-count');

			     if (liked == 1) {
                 $btn.attr('liked', '0');
                 $btn.removeClass('liked');
                 $btn.html('<i class="fa fa-thumbs-up me-2"></i>Like (<span class="like-count">' + (parseInt($count.text()) - 1) + '</span>)');
			     }else {
                 $btn.attr('liked', '1');
                 $btn.addClass('liked');
                 $btn.html('<i class="fa fa-thumbs-up me-2"></i>Liked (<span class="like-count">' + (parseInt($count.text()) + 1) + '</span>)');
			     }

			     // Update like count via AJAX
			     $.post("ajax/like-unlike.php", {
			     	post_id: post_id
			     });
			  });
		  });

		  // Additional functions
		  function scrollToComments() {
		      $('html, body').animate({
		          scrollTop: $('#comments').offset().top - 100
		      }, 800);
		  }

		  function sharePost() {
		      if (navigator.share) {
		          navigator.share({
		              title: '<?=htmlspecialchars($post['post_title'])?>',
		              text: '<?=htmlspecialchars(substr(strip_tags($post['post_text']), 0, 100))?>...',
		              url: window.location.href
		          });
		      } else {
		          // Fallback: copy to clipboard
		          navigator.clipboard.writeText(window.location.href).then(function() {
		              alert('Link copied to clipboard!');
		          });
		      }
		  }
   </script>

	<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js" integrity="sha384-geWF76RCwLtnZ8qwWowPQNguL3RmwHVBC9FhGdlKrxdiJJigb/j/68SIy3Te4Bkz" crossorigin="anonymous"></script>

    <!-- Footer -->
    <?php include 'inc/footer.php'; ?>
</body>
</html>
<?php }else {
	header("Location: blog.php");
	exit;
} ?>