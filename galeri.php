<?php
ob_start();
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
include_once("db_conn.php");
date_default_timezone_set('Asia/Jakarta');

// Get category filter
$category_filter = isset($_GET['kategori']) ? trim($_GET['kategori']) : '';

// Get all unique categories for filter
$categories = [];
try {
    $stmt = $conn->prepare("SELECT DISTINCT kategori FROM galeri WHERE kategori IS NOT NULL AND kategori != '' ORDER BY kategori ASC");
    $stmt->execute();
    $categories = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (Exception $e) {
    error_log("Categories query error: " . $e->getMessage());
}

// Get gallery data with category filtering
try {
    if ($category_filter) {
        $stmt = $conn->prepare("SELECT * FROM galeri WHERE kategori = ? ORDER BY created_at DESC, id DESC");
        $stmt->execute([$category_filter]);
    } else {
        $stmt = $conn->prepare("SELECT * FROM galeri ORDER BY created_at DESC, id DESC");
        $stmt->execute();
    }
    $galeri = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    // Fallback for different column names
    try {
        if ($category_filter) {
            $stmt = $conn->prepare("SELECT * FROM galeri WHERE kategori = ? ORDER BY tanggal DESC, id DESC");
            $stmt->execute([$category_filter]);
        } else {
            $stmt = $conn->prepare("SELECT * FROM galeri ORDER BY tanggal DESC, id DESC");
            $stmt->execute();
        }
        $galeri = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e2) {
        $galeri = [];
        error_log("Gallery query error: " . $e2->getMessage());
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Galeri - UKM Panahan Gendewa Geni</title>
    <meta name="description" content="Galeri foto dan video kegiatan UKM Panahan Gendewa Geni Universitas Semarang">
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/font-awesome.min.css">
    <link rel="stylesheet" href="css/animate.css">
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Merriweather:wght@400;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Lightbox CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.4/css/lightbox.min.css">

    <style>
        :root {
            --primary-color: #005a99;
            --secondary-color: #ff8800;
            --dark-color: #1a1a1a;
            --light-color: #f8f9fa;
            --text-color: #333;
            --border-radius: 20px;
            --shadow-light: 0 4px 20px rgba(0,0,0,0.08);
            --shadow-medium: 0 8px 30px rgba(0,0,0,0.12);
            --shadow-heavy: 0 15px 50px rgba(0,0,0,0.15);
        }

        body {
            background: #ffffff;
            min-height: 100vh;
            font-family: 'Poppins', sans-serif;
            color: var(--text-color);
            line-height: 1.6;
        }

        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            padding: 100px 0 80px;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(0,90,153,0.95), rgba(255,136,0,0.9));
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-family: 'Merriweather', serif;
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            animation: fadeInDown 1s ease-out;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            font-weight: 300;
            margin-bottom: 30px;
            opacity: 0.95;
            animation: fadeInUp 1s ease-out 0.3s both;
        }

        .hero-badge {
            background: rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            font-weight: 600;
            font-size: 1rem;
            border-radius: 50px;
            padding: 12px 30px;
            display: inline-block;
            margin-bottom: 30px;
            letter-spacing: 1px;
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        /* Gallery Section */
        .gallery-section {
            padding: 80px 0;
            background: #ffffff;
        }

        .section-header {
            text-align: center;
            margin-bottom: 60px;
        }

        .section-title {
            font-family: 'Merriweather', serif;
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 20px;
        }

        .section-subtitle {
            font-size: 1.2rem;
            color: #666;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Category Filter */
        .filter-section {
            background: #f8f9fa;
            padding: 30px 0;
            margin-bottom: 40px;
            border-radius: 20px;
        }

        .filter-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 20px;
            text-align: center;
        }

        .filter-buttons {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 12px;
        }

        .filter-btn {
            background: white;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .filter-btn:hover {
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,90,153,0.3);
        }

        .filter-btn.active {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-color: var(--secondary-color);
        }

        .filter-btn.active:hover {
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
        }

        /* Gallery Grid */
        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .gallery-item {
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-light);
            transition: all 0.3s ease;
            position: relative;
            animation: fadeInUp 0.6s ease-out;
        }

        .gallery-item:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-heavy);
        }

        .gallery-media {
            position: relative;
            width: 100%;
            height: 280px;
            overflow: hidden;
            background: #f8f9fa;
        }

        .gallery-img, .gallery-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .gallery-item:hover .gallery-img {
            transform: scale(1.05);
        }

        .media-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.7) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .gallery-item:hover .media-overlay {
            opacity: 1;
        }

        .media-icon {
            background: rgba(255,255,255,0.9);
            color: var(--primary-color);
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            transform: scale(0.8);
            transition: transform 0.3s ease;
        }

        .gallery-item:hover .media-icon {
            transform: scale(1);
        }

        .gallery-content {
            padding: 25px;
        }

        .gallery-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .gallery-description {
            color: #666;
            font-size: 0.95rem;
            line-height: 1.6;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .gallery-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }

        .gallery-date {
            color: #999;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .gallery-type {
            background: var(--primary-color);
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .gallery-type.video {
            background: var(--secondary-color);
        }

        .gallery-category {
            margin-top: 12px;
        }

        .gallery-category .badge {
            background: #6c757d !important;
            color: white;
            font-size: 0.75rem;
            padding: 4px 8px;
            border-radius: 12px;
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 80px 20px;
            color: #666;
        }

        .empty-state i {
            font-size: 4rem;
            color: #ddd;
            margin-bottom: 20px;
        }

        .empty-state h3 {
            font-size: 1.5rem;
            margin-bottom: 10px;
            color: var(--primary-color);
        }

        /* Modal Enhancements */
        .modal-content {
            border: none;
            border-radius: var(--border-radius);
            overflow: hidden;
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            padding: 20px 30px;
        }

        .modal-title {
            font-weight: 600;
            font-size: 1.3rem;
        }

        .modal-body {
            padding: 30px;
            background: #f8f9fa;
        }

        .modal-img {
            max-width: 100%;
            max-height: 70vh;
            border-radius: 15px;
            box-shadow: var(--shadow-medium);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.1rem;
            }

            .gallery-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .gallery-media {
                height: 250px;
            }

            .gallery-content {
                padding: 20px;
            }

            .section-title {
                font-size: 2rem;
            }

            .filter-section {
                padding: 20px 0;
                margin-bottom: 30px;
            }

            .filter-buttons {
                gap: 8px;
            }

            .filter-btn {
                padding: 8px 16px;
                font-size: 0.9rem;
            }
        }

        @media (max-width: 576px) {
            .hero-section {
                padding: 80px 0 60px;
            }

            .hero-title {
                font-size: 2rem;
            }

            .gallery-section {
                padding: 40px 0;
            }

            .gallery-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .gallery-media {
                height: 220px;
            }

            .filter-section {
                padding: 15px;
                margin-bottom: 20px;
            }

            .filter-title {
                font-size: 1.1rem;
                margin-bottom: 15px;
            }

            .filter-btn {
                padding: 6px 12px;
                font-size: 0.8rem;
                border-radius: 20px;
            }

            .section-title {
                font-size: 1.8rem;
            }

            .section-subtitle {
                font-size: 1rem;
            }
        }

        /* Animations */
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Loading Animation */
        .loading {
            text-align: center;
            padding: 40px;
        }

        .loading i {
            font-size: 2rem;
            color: var(--primary-color);
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <?php include 'inc/navbar.php'; ?>

    <!-- Hero Section -->
    <section class="pt-120 pb-60" style="background: linear-gradient(rgba(0,90,153,0.8), rgba(255,152,0,0.8)), url('upload/services-img.jpg'); background-size: cover; background-position: center; background-repeat: no-repeat;">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h1 class="text-white mb-4" style="font-size: 2.5rem; font-weight: 700; text-shadow: 2px 2px 4px rgba(0,0,0,0.5);">
                        <i class="fa fa-bullseye mr-3"></i>Galeri UKM Panahan Gendewa Geni
                    </h1>
                    
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Section -->
    <section class="gallery-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Koleksi Foto & Video</h2>
                <p class="section-subtitle">
                    Kumpulan dokumentasi kegiatan, latihan, dan prestasi UKM Panahan Gendewa Geni Universitas Semarang
                </p>
            </div>

            <!-- Category Filter -->
            <?php if (!empty($categories)): ?>
            <div class="filter-section">
                <h3 class="filter-title">
                    <i class="fa fa-filter me-2"></i>Filter Berdasarkan Kategori
                </h3>
                <div class="filter-buttons">
                    <a href="galeri.php" class="filter-btn <?= empty($category_filter) ? 'active' : '' ?>">
                        <i class="fa fa-th-large"></i>
                        Semua (<?= count($galeri) ?>)
                    </a>
                    <?php foreach ($categories as $cat): ?>
                        <?php
                        // Count items in this category
                        $count_stmt = $conn->prepare("SELECT COUNT(*) FROM galeri WHERE kategori = ?");
                        $count_stmt->execute([$cat]);
                        $count = $count_stmt->fetchColumn();
                        ?>
                        <a href="galeri.php?kategori=<?= urlencode($cat) ?>"
                           class="filter-btn <?= $category_filter === $cat ? 'active' : '' ?>">
                            <i class="fa fa-tag"></i>
                            <?= htmlspecialchars($cat) ?> (<?= $count ?>)
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Current Filter Display -->
            <?php if ($category_filter): ?>
            <div class="text-center mb-4">
                <div class="alert alert-info d-inline-block">
                    <i class="fa fa-info-circle me-2"></i>
                    Menampilkan galeri kategori: <strong><?= htmlspecialchars($category_filter) ?></strong>
                    <a href="galeri.php" class="btn btn-sm btn-outline-primary ms-2">
                        <i class="fa fa-times me-1"></i>Hapus Filter
                    </a>
                </div>
            </div>
            <?php endif; ?>

            <?php if (empty($galeri)): ?>
                <!-- Empty State -->
                <div class="empty-state">
                    <i class="fa fa-<?= $category_filter ? 'filter' : 'camera' ?>"></i>
                    <h3><?= $category_filter ? 'Tidak Ada Galeri dalam Kategori Ini' : 'Belum Ada Galeri' ?></h3>
                    <p>
                        <?php if ($category_filter): ?>
                            Tidak ditemukan foto atau video dalam kategori <strong><?= htmlspecialchars($category_filter) ?></strong>.
                            <br><a href="galeri.php" class="btn btn-primary mt-3">
                                <i class="fa fa-th-large me-2"></i>Lihat Semua Galeri
                            </a>
                        <?php else: ?>
                            Galeri foto dan video akan ditampilkan di sini.
                        <?php endif; ?>
                    </p>
                </div>
            <?php else: ?>
                <!-- Gallery Grid -->
                <div class="gallery-grid">
                    <?php foreach($galeri as $index => $g): ?>
                        <?php
                        // Handle different possible column names
                        $judul = $g['judul'] ?? $g['title'] ?? 'Tanpa Judul';
                        $deskripsi = $g['deskripsi'] ?? $g['description'] ?? '';
                        $gambar = $g['gambar'] ?? $g['image'] ?? $g['file'] ?? '';
                        $tanggal = $g['created_at'] ?? $g['tanggal'] ?? $g['date'] ?? date('Y-m-d');

                        // Check file type
                        $ext = strtolower(pathinfo($gambar, PATHINFO_EXTENSION));
                        $isVideo = in_array($ext, ['mp4', 'webm', 'ogg', 'avi', 'mov']);
                        $mediaPath = 'upload/galeri/' . htmlspecialchars($gambar);
                        ?>

                        <div class="gallery-item" style="animation-delay: <?= $index * 0.1 ?>s">
                            <div class="gallery-media">
                                <?php if ($isVideo): ?>
                                    <video class="gallery-video" preload="metadata" controls>
                                        <source src="<?= $mediaPath ?>" type="video/mp4">
                                        Video tidak didukung.
                                    </video>
                                    <div class="media-overlay video-overlay" data-video="<?= $mediaPath ?>" data-title="<?= htmlspecialchars($judul) ?>">
                                        <div class="media-icon">
                                            <i class="fa fa-play"></i>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <a href="<?= $mediaPath ?>"
                                       data-lightbox="gallery"
                                       data-title="<?= htmlspecialchars($judul) ?><?= !empty($deskripsi) ? ' - ' . htmlspecialchars($deskripsi) : '' ?>">
                                        <img src="<?= $mediaPath ?>"
                                             class="gallery-img"
                                             alt="<?= htmlspecialchars($judul) ?>"
                                             loading="lazy">
                                        <div class="media-overlay">
                                            <div class="media-icon">
                                                <i class="fa fa-search-plus"></i>
                                            </div>
                                        </div>
                                    </a>
                                <?php endif; ?>
                            </div>

                            <div class="gallery-content">
                                <h3 class="gallery-title"><?= htmlspecialchars($judul) ?></h3>

                                <?php if (!empty($deskripsi)): ?>
                                    <p class="gallery-description"><?= htmlspecialchars($deskripsi) ?></p>
                                <?php endif; ?>

                                <div class="gallery-meta">
                                    <div class="gallery-date">
                                        <i class="fa fa-calendar"></i>
                                        <?= date('d M Y', strtotime($tanggal)) ?>
                                    </div>
                                </div>

                                <?php if (!empty($g['kategori'])): ?>
                                <div class="gallery-category mt-2">
                                    <span class="badge bg-secondary">
                                        <i class="fa fa-tag me-1"></i><?= htmlspecialchars($g['kategori']) ?>
                                    </span>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Modal untuk preview video -->
    <div class="modal fade" id="videoModal" tabindex="-1" role="dialog" aria-labelledby="videoModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="videoModalLabel">
                        <i class="fa fa-video-camera mr-2"></i>Preview Video
                    </h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center">
                    <video id="videoModalPlayer" controls style="max-width: 100%; max-height: 70vh; border-radius: 15px;">
                        <source id="videoModalSource" src="" type="video/mp4">
                        Browser Anda tidak mendukung video HTML5.
                    </video>
                </div>
            </div>
        </div>
    </div>
    <!-- Footer -->
    <footer>
        <?php include 'inc/footer.php'; ?>
    </footer>

    <!-- Scripts -->
    <script src="js/vendor/jquery-1.12.4.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/jquery.counterup.min.js"></script>
    <script src="js/waypoints.min.js"></script>
    <script src="js/animate.js"></script>
    <!-- Lightbox JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lightbox2/2.11.4/js/lightbox.min.js"></script>

    <script>
        $(document).ready(function() {
            // Initialize dropdown
            $('.dropdown-toggle').dropdown();

            // Configure Lightbox
            lightbox.option({
                'resizeDuration': 200,
                'wrapAround': true,
                'albumLabel': "Gambar %1 dari %2",
                'fadeDuration': 300,
                'imageFadeDuration': 300
            });

            // Video modal functionality
            $('.video-overlay').on('click', function() {
                var videoSrc = $(this).data('video');
                var title = $(this).data('title') || 'Video Galeri';

                $('#videoModalSource').attr('src', videoSrc);
                $('#videoModalPlayer')[0].load(); // Reload video element
                $('#videoModalLabel').html('<i class="fa fa-video-camera mr-2"></i>' + title);
                $('#videoModal').modal('show');
            });

            // Pause video when modal is closed
            $('#videoModal').on('hidden.bs.modal', function() {
                $('#videoModalPlayer')[0].pause();
            });

            // Lazy loading for images
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src || img.src;
                            img.classList.remove('lazy');
                            imageObserver.unobserve(img);
                        }
                    });
                });

                document.querySelectorAll('img[loading="lazy"]').forEach(img => {
                    imageObserver.observe(img);
                });
            }

            // Smooth scrolling for anchor links
            $('a[href^="#"]').on('click', function(event) {
                var target = $(this.getAttribute('href'));
                if (target.length) {
                    event.preventDefault();
                    $('html, body').stop().animate({
                        scrollTop: target.offset().top - 100
                    }, 1000);
                }
            });

            // Gallery item animations on scroll
            function animateOnScroll() {
                $('.gallery-item').each(function() {
                    var elementTop = $(this).offset().top;
                    var elementBottom = elementTop + $(this).outerHeight();
                    var viewportTop = $(window).scrollTop();
                    var viewportBottom = viewportTop + $(window).height();

                    if (elementBottom > viewportTop && elementTop < viewportBottom) {
                        $(this).addClass('animate__animated animate__fadeInUp');
                    }
                });
            }

            // Initial check
            animateOnScroll();

            // Check on scroll
            $(window).on('scroll', function() {
                animateOnScroll();
            });

            // Error handling for images
            $('.gallery-img').on('error', function() {
                $(this).attr('src', 'img/no-image.jpg');
                $(this).attr('alt', 'Gambar tidak tersedia');
            });

            // Video error handling
            $('.gallery-video').on('error', function() {
                $(this).parent().html('<div class="text-center p-4"><i class="fa fa-exclamation-triangle fa-2x text-warning"></i><br>Video tidak dapat dimuat</div>');
            });

            // Filter button animations
            $('.filter-btn').hover(
                function() {
                    $(this).addClass('animate__animated animate__pulse');
                },
                function() {
                    $(this).removeClass('animate__animated animate__pulse');
                }
            );

            // Smooth scroll to gallery when filter is clicked
            $('.filter-btn').on('click', function() {
                setTimeout(function() {
                    $('html, body').animate({
                        scrollTop: $('.gallery-grid').offset().top - 100
                    }, 500);
                }, 100);
            });

            // Gallery statistics
            var totalItems = $('.gallery-item').length;
            var photoCount = $('.gallery-item .gallery-img').length;
            var videoCount = $('.gallery-item .gallery-video').length;

            console.log('Gallery loaded:', {
                total: totalItems,
                photos: photoCount,
                videos: videoCount
            });
        });
    </script>
</body>
</html>
